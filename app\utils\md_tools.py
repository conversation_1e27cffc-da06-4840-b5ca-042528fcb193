import datetime
import json
import os
import re
import logging
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

import pandas as pd

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    FileProcessingException, DataProcessingException, ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

class MarkdownTools:
    @handle_layer_boundary(LayerType.UTILITY, "MarkdownTools initialization")
    def __init__(self, size_limit: int = 10000, pdf_processing_mode: str = "balanced"):
        """Initialize with default size limit and patterns with unified error handling"""
        set_operation_context("markdown_tools_init")

        with error_boundary("parameter validation", LayerType.UTILITY):
            if size_limit <= 0:
                raise ValidationException(
                    message="Size limit must be positive",
                    field_name="size_limit",
                    details=f"Size limit must be greater than 0, got: {size_limit}",
                    context={'provided_size_limit': size_limit},
                    suggested_action="Provide a positive size limit value"
                )

            # Validate PDF processing mode
            valid_modes = ["fast", "balanced", "accurate"]
            if pdf_processing_mode not in valid_modes:
                raise ValidationException(
                    message="Invalid PDF processing mode",
                    field_name="pdf_processing_mode",
                    details=f"Mode must be one of {valid_modes}, got: {pdf_processing_mode}",
                    context={'provided_mode': pdf_processing_mode, 'valid_modes': valid_modes},
                    suggested_action="Use 'fast' for speed, 'balanced' for balance, or 'accurate' for maximum quality"
                )

            self.size_limit = size_limit
            self.pdf_processing_mode = pdf_processing_mode
            logger.debug(f"Initializing MarkdownTools with size_limit={size_limit}, pdf_mode={pdf_processing_mode}")

        with error_boundary("pattern compilation", LayerType.UTILITY):
            try:
                # Patterns from md_splitter
                self.header_pattern = re.compile(r'^([#*]{1,6})\s+(.*)$', re.MULTILINE)
                self.table_header_pattern = re.compile(r'^\|(.+?)\|', re.MULTILINE)
                self.table_title_pattern = re.compile(r'^\|.*?表.*?\|', re.MULTILINE)

            except re.error as e:
                raise ConfigurationException(
                    message="Failed to compile regex patterns",
                    details=f"Regex compilation error: {str(e)}",
                    original_exception=e,
                    context={'patterns': ['header_pattern', 'table_header_pattern', 'table_title_pattern']},
                    suggested_action="Check regex pattern syntax"
                )

        # State for table processing
        self.pdf_options = None
        self._pdf_converter = None
    
    @handle_layer_boundary(LayerType.UTILITY, "markdown content splitting")
    def split(self, content: str) -> List[str]:
        """Split markdown content by headers recursively with unified error handling"""
        set_operation_context("markdown_content_splitting")

        # Input validation
        with error_boundary("input validation", LayerType.UTILITY):
            if content is None:
                raise ValidationException(
                    message="Content cannot be None",
                    field_name="content",
                    details="Content parameter is None",
                    context={'content_type': type(content).__name__},
                    suggested_action="Provide valid markdown content string"
                )

            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__, 'content_length': len(str(content)) if content else 0},
                    suggested_action="Provide a valid string content"
                )

        logger.debug(f"Splitting markdown content (length={len(content)})")

        # Check size limit
        with error_boundary("size check", LayerType.UTILITY):
            if len(content) <= self.size_limit:
                logger.debug("Content fits within size limit, returning as single chunk")
                return [content]

        # Find headers
        with error_boundary("header extraction", LayerType.UTILITY):
            try:
                headers = []
                for match in self.header_pattern.finditer(content):
                    headers.append((match.start(), match.group(0)))

                if not headers:
                    logger.debug("No headers found, using fallback splitting")
                    return self._fallback_split(content)

            except re.error as e:
                raise DataProcessingException(
                    message="Failed to extract headers from content",
                    data_type="header_extraction",
                    details=f"Regex pattern matching error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'pattern': str(self.header_pattern.pattern)},
                    suggested_action="Check content format and header patterns"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Unexpected error during header extraction",
                    data_type="header_extraction",
                    details=f"Header extraction error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and header patterns"
                )

        # Split content by headers
        with error_boundary("content splitting", LayerType.UTILITY):
            try:
                for i, (pos, header) in enumerate(headers):
                    next_pos = headers[i+1][0] if i+1 < len(headers) else len(content)
                    chunk = content[pos:next_pos]

                    if len(chunk) > self.size_limit:
                        # Recursive splitting needed
                        sub_chunks = self.split(chunk)
                        if len(sub_chunks) > 1:
                            result = []
                            if pos > 0:
                                result.append(content[:pos])
                            result.extend(sub_chunks)
                            if next_pos < len(content):
                                result.append(content[next_pos:])
                            return result

                # If no recursive splitting was needed, use fallback
                return self._fallback_split(content)

            except RecursionError as e:
                raise DataProcessingException(
                    message="Maximum recursion depth exceeded during content splitting",
                    data_type="recursive_splitting",
                    details="Content structure caused infinite recursion",
                    original_exception=e,
                    context={'content_length': len(content), 'headers_count': len(headers)},
                    suggested_action="Reduce content complexity or increase size limit"
                )
            except Exception as e:
                if isinstance(e, (ValidationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to split content by headers",
                    data_type="content_splitting",
                    details=f"Content splitting error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'headers_count': len(headers)},
                    suggested_action="Check content structure and size limits"
                )
        
    @handle_layer_boundary(LayerType.UTILITY, "fallback content splitting")
    def _fallback_split(self, content: str) -> List[str]:
        """Fallback splitting method when header splitting isn't possible"""
        set_operation_context("fallback_splitting")
        
        with error_boundary("fallback splitting logic", LayerType.UTILITY):
            try:
                if self._has_table(content):
                    return self._split_table(content)
                return self._split_lines(content)
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to perform fallback content splitting",
                    data_type="fallback_splitting",
                    details=f"Error during fallback splitting: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content structure and fallback splitting logic"
                )
        
    @log_and_reraise(logger, "table detection")
    def _has_table(self, content: str) -> bool:
        """Check if content contains markdown tables"""
        with error_boundary("table pattern matching", LayerType.UTILITY):
            try:
                return bool(self.table_header_pattern.search(content))
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to detect tables in content",
                    data_type="table_detection",
                    details=f"Regex pattern matching error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'pattern': str(self.table_header_pattern.pattern)},
                    suggested_action="Check content format and table patterns"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Unexpected error during table detection",
                    data_type="table_detection",
                    details=f"Table detection error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and table patterns"
                )
        
    @log_and_reraise(logger, "table content splitting")
    def _split_table(self, content: str) -> List[str]:
        """Split content with tables while preserving all content and structure"""
        set_operation_context("table_splitting")
        
        with error_boundary("table structure analysis", LayerType.UTILITY):
            try:
                lines = content.split('\n')
                chunks = []
                current_chunk = []
                in_table = False
                table_header = None
                context_before_table = []
                markdown_header = None
                table_title_header = None
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to initialize table splitting",
                    data_type="table_structure",
                    details=f"Table structure analysis error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and structure"
                )
        
        with error_boundary("table line processing", LayerType.UTILITY):
            try:
                for line_num, line in enumerate(lines):
                    is_table_row = line.strip().startswith('|')
                    is_markdown_header = line.strip().startswith('#')
                    is_table_title = (is_table_row and 
                                    self.table_title_pattern.search(line) and
                                    not table_title_header)
                    
                    if is_table_row:
                        if not in_table:
                            # Starting a new table
                            in_table = True
                            table_header = line if line.strip() else None
                            current_chunk.extend(context_before_table)
                            context_before_table = []
                            if table_title_header:
                                current_chunk.append(table_title_header)
                                table_title_header = None
                            elif markdown_header:
                                current_chunk.append(markdown_header)
                                markdown_header = None
                            current_chunk.append(line)
                        else:
                            current_chunk.append(line)
                    else:
                        if in_table:
                            # End of table
                            if len('\n'.join(current_chunk)) > self.size_limit:
                                table_chunks = self._split_table_lines(current_chunk, table_header)
                                chunks.extend(table_chunks[:-1])
                                current_chunk = table_chunks[-1].split('\n') if table_chunks else []
                            else:
                                chunks.append('\n'.join(current_chunk))
                                current_chunk = []
                            in_table = False
                            table_header = None
                        
                        if is_table_title:
                            table_title_header = line
                        elif is_markdown_header and not markdown_header:
                            markdown_header = line
                        else:
                            context_before_table.append(line)
                        
                    # Check if current chunk exceeds size limit
                    if len('\n'.join(current_chunk)) >= self.size_limit:
                        if in_table and table_header:
                            table_chunks = self._split_table_lines(current_chunk, table_header)
                            chunks.extend(table_chunks[:-1])
                            current_chunk = table_chunks[-1].split('\n') if table_chunks else []
                        else:
                            chunks.append('\n'.join(current_chunk))
                            current_chunk = []
                            
                # Handle remaining content
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                if context_before_table:
                    chunks.append('\n'.join(context_before_table))
                    
                return [chunk for chunk in chunks if chunk.strip()]
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to process table lines",
                    data_type="table_line_processing",
                    details=f"Table line processing error: {str(e)}",
                    original_exception=e,
                    context={'total_lines': len(lines), 'current_line': line_num if 'line_num' in locals() else 0},
                    suggested_action="Check table structure and content format"
                )
        
    @log_and_reraise(logger, "table line splitting")
    def _split_table_lines(self, lines: List[str], header: str) -> List[str]:
        """Split table lines into chunks while preserving structure and context"""
        set_operation_context("table_line_splitting")
        
        with error_boundary("table line validation", LayerType.UTILITY):
            if not lines:
                return []
            
            if not isinstance(lines, list):
                raise ValidationException(
                    message="Lines must be a list",
                    field_name="lines",
                    details=f"Expected list, got {type(lines)}",
                    context={'input_type': type(lines).__name__},
                    suggested_action="Provide a valid list of strings"
                )
                
        with error_boundary("table chunk generation", LayerType.UTILITY):
            try:
                chunks = []
                current_chunk = []
                prev_chunk_tail = []
                
                # Find table start
                table_start = 0
                for i, line in enumerate(lines):
                    if line.strip().startswith('|'):
                        table_start = i
                        break
                        
                # Add content before table to current chunk
                if table_start > 0:
                    current_chunk.extend(lines[:table_start])
                    
                table_header = lines[table_start] if table_start < len(lines) else header or ""
                
                # Process table lines
                for line in lines[table_start:]:
                    # Check if adding this line would exceed size limit
                    test_chunk = current_chunk + [table_header, line] if table_header not in current_chunk else current_chunk + [line]
                    if len('\n'.join(test_chunk)) > self.size_limit and len(current_chunk) > 0:
                        # Create overlap for continuity
                        non_empty_lines = [l for l in '\n'.join(current_chunk).split('\n') if l.strip()]
                        overlap = max(1, int(len(non_empty_lines) * 0.1))
                        prev_chunk_tail = non_empty_lines[-overlap:]
                        
                        chunks.append('\n'.join(current_chunk))
                        current_chunk = []
                        
                        # Start new chunk with context
                        if table_start > 0:
                            current_chunk.extend(lines[:table_start])
                        current_chunk.append(table_header)
                        if prev_chunk_tail:
                            current_chunk.extend(prev_chunk_tail)
                        
                    # Ensure table header is present
                    if not current_chunk and table_start > 0:
                        current_chunk.extend(lines[:table_start])
                        current_chunk.append(table_header)
                    elif table_header not in current_chunk:
                        current_chunk.append(table_header)
                        
                    current_chunk.append(line)
                    
                # Add final chunk
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                    
                return chunks
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to split table lines into chunks",
                    data_type="table_chunk_generation",
                    details=f"Table chunk generation error: {str(e)}",
                    original_exception=e,
                    context={'lines_count': len(lines), 'table_start': table_start if 'table_start' in locals() else 0},
                    suggested_action="Check table structure and size limits"
                )
            
    @log_and_reraise(logger, "line-based content splitting")
    def _split_lines(self, content: str) -> List[str]:
        """Split non-table content by lines while preserving paragraphs"""
        set_operation_context("line_splitting")
        
        with error_boundary("paragraph splitting", LayerType.UTILITY):
            try:
                paragraphs = content.split('\n\n')
                chunks = []
                current_chunk = []
                prev_chunk_tail = []
                
                for para_num, para in enumerate(paragraphs):
                    # Check if adding this paragraph would exceed size limit
                    test_content = '\n\n'.join(current_chunk + [para])
                    if len(test_content) > self.size_limit and current_chunk:
                        # Create overlap for continuity
                        current_content = '\n\n'.join(current_chunk)
                        non_empty_lines = [line for line in current_content.split('\n') if line.strip()]
                        overlap = max(1, int(len(non_empty_lines) * 0.1))
                        prev_chunk_tail = non_empty_lines[-overlap:]
                        
                        chunks.append(current_content)
                        current_chunk = []
                        if prev_chunk_tail:
                            current_chunk.append('\n'.join(prev_chunk_tail))
                    
                    current_chunk.append(para)
                    
                # Add final chunk
                if current_chunk:
                    chunks.append('\n\n'.join(current_chunk))
                    
                return [chunk for chunk in chunks if chunk.strip()]
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to split content by lines",
                    data_type="line_splitting",
                    details=f"Line splitting error: {str(e)}",
                    original_exception=e,
                    context={'paragraphs_count': len(paragraphs) if 'paragraphs' in locals() else 0, 'content_length': len(content)},
                    suggested_action="Check content structure and paragraph formatting"
                )

    @handle_layer_boundary(LayerType.UTILITY, "markdown chunk merging")
    def merge_chunks(self, chunks: List[str]) -> str:
        """Merge split markdown chunks back into original format"""
        set_operation_context("chunk_merging")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if chunks is None:
                raise ValidationException(
                    message="Chunks cannot be None",
                    field_name="chunks",
                    details="Chunks parameter is None",
                    context={'chunks_type': type(chunks).__name__},
                    suggested_action="Provide a valid list of chunks to merge"
                )
            
            if not chunks:
                raise ValidationException(
                    message="Chunks list cannot be empty",
                    field_name="chunks",
                    details="Empty chunks list provided",
                    context={'chunks_length': len(chunks)},
                    suggested_action="Provide a non-empty list of chunks to merge"
                )
            
            if not isinstance(chunks, list):
                raise ValidationException(
                    message="Chunks must be a list",
                    field_name="chunks",
                    details=f"Expected list, got {type(chunks)}",
                    context={'input_type': type(chunks).__name__},
                    suggested_action="Provide a valid list of string chunks"
                )

        logger.debug(f"Merging {len(chunks)} markdown chunks")
        
        with error_boundary("chunk processing", LayerType.UTILITY):
            try:
                cleaned_chunks = []
                in_table = False
                table_header = None
                last_header = None
                
                for i, chunk in enumerate(chunks):
                    if not isinstance(chunk, str):
                        raise ValidationException(
                            message=f"Chunk {i} is not a string",
                            field_name=f"chunks[{i}]",
                            details=f"Expected string, got {type(chunk)}",
                            context={'chunk_index': i, 'chunk_type': type(chunk).__name__},
                            suggested_action="Ensure all chunks are strings"
                        )
                    
                    # Process chunk content
                    lines = chunk.split('\n')
                    processed_lines = []
                    
                    for line_num, line in enumerate(lines):
                        # Handle table detection and processing
                        if line.strip().startswith('|'):
                            if not in_table:
                                in_table = True
                                table_header = line
                            processed_lines.append(line)
                        else:
                            if in_table:
                                in_table = False
                                table_header = None
                            
                            # Track markdown headers
                            if line.strip().startswith('#'):
                                last_header = line
                            
                            processed_lines.append(line)
                    
                    cleaned_chunks.append('\n'.join(processed_lines))
                
                return '\n'.join(cleaned_chunks)
                
            except Exception as e:
                if isinstance(e, (ValidationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to merge markdown chunks",
                    data_type="chunk_merging",
                    details=f"Chunk merging error: {str(e)}",
                    original_exception=e,
                    context={'chunks_count': len(chunks), 'current_chunk': i if 'i' in locals() else 0},
                    suggested_action="Check chunk format and content structure"
                )

    @handle_layer_boundary(LayerType.UTILITY, "markdown content analysis")
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """Analyze markdown content structure and statistics"""
        set_operation_context("content_analysis")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not content or not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a non-empty string",
                    field_name="content",
                    details=f"Expected non-empty string, got {type(content)} with length {len(content) if content else 0}",
                    context={'content_type': type(content).__name__, 'content_length': len(content) if content else 0},
                    suggested_action="Provide valid markdown content string"
                )

        with error_boundary("content structure analysis", LayerType.UTILITY):
            try:
                analysis = {
                    'total_length': len(content),
                    'line_count': len(content.split('\n')),
                    'paragraph_count': len([p for p in content.split('\n\n') if p.strip()]),
                    'header_count': 0,
                    'table_count': 0,
                    'headers': [],
                    'tables': [],
                    'estimated_chunks': 0
                }
                
                # Analyze headers
                for match in self.header_pattern.finditer(content):
                    analysis['header_count'] += 1
                    analysis['headers'].append({
                        'level': len(match.group(1)),
                        'text': match.group(2).strip(),
                        'position': match.start()
                    })
                
                # Analyze tables
                lines = content.split('\n')
                in_table = False
                current_table = []
                
                for line in lines:
                    if line.strip().startswith('|'):
                        if not in_table:
                            in_table = True
                            current_table = [line]
                        else:
                            current_table.append(line)
                    else:
                        if in_table:
                            analysis['table_count'] += 1
                            analysis['tables'].append({
                                'row_count': len(current_table),
                                'content_length': len('\n'.join(current_table))
                            })
                            in_table = False
                            current_table = []
                
                # Handle table at end of content
                if in_table and current_table:
                    analysis['table_count'] += 1
                    analysis['tables'].append({
                        'row_count': len(current_table),
                        'content_length': len('\n'.join(current_table))
                    })
                
                # Estimate chunk count
                analysis['estimated_chunks'] = max(1, len(content) // self.size_limit)
                
                return analysis
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to analyze markdown content",
                    data_type="content_analysis",
                    details=f"Content analysis error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and structure"
                )

    # --------------------------
    # Methods from md_table_splitter.py
    # --------------------------

    @handle_layer_boundary(LayerType.UTILITY, "number formatting in content")
    def format_numbers_in_content(self, content: str) -> str:
        """Format numbers in content by removing thousand separators"""
        set_operation_context("number_formatting")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__},
                    suggested_action="Provide valid string content"
                )
        
        with error_boundary("number pattern processing", LayerType.UTILITY):
            try:
                number_pattern = r'(?<=\|)\s*([+-]?\d{1,3}(?:[,\u00A0\u202F]\d{3})*(?:\.\d+)?)\s*(?=\|)'
                
                def format_number(match):
                    return match.group(1).replace(',', '').replace('\u00A0', '').replace('\u202F', '')
                    
                formatted_content = re.sub(number_pattern, format_number, content)
                logger.debug(f"Formatted numbers in content (length={len(content)})")
                return formatted_content
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to format numbers in content",
                    data_type="number_formatting",
                    details=f"Regex processing error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'pattern': number_pattern},
                    suggested_action="Check content format and number patterns"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Unexpected error during number formatting",
                    data_type="number_formatting",
                    details=f"Number formatting error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content structure and number format"
                )

    @handle_layer_boundary(LayerType.UTILITY, "table-based content splitting")
    def split_by_tables(self, input_file: str, output_dir: str, format_dates: bool = False, format_numbers: bool = False) -> List[str]:
        """Split markdown file by tables with optional formatting"""
        set_operation_context("table_based_splitting")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not isinstance(input_file, str) or not input_file.strip():
                raise ValidationException(
                    message="Input file path must be a non-empty string",
                    field_name="input_file",
                    details=f"Expected non-empty string, got {type(input_file)}",
                    context={'input_file_type': type(input_file).__name__},
                    suggested_action="Provide a valid file path string"
                )
            
            if not isinstance(output_dir, str) or not output_dir.strip():
                raise ValidationException(
                    message="Output directory must be a non-empty string",
                    field_name="output_dir",
                    details=f"Expected non-empty string, got {type(output_dir)}",
                    context={'output_dir_type': type(output_dir).__name__},
                    suggested_action="Provide a valid directory path string"
                )
        
        with error_boundary("file existence check", LayerType.UTILITY):
            input_path = Path(input_file)
            if not input_path.exists():
                raise FileProcessingException(
                    message="Input file not found",
                    file_path=input_file,
                    details=f"File does not exist: {input_file}",
                    context={'file_path': input_file, 'resolved_path': str(input_path.resolve()), 'operation': 'read'},
                    suggested_action="Check the file path and ensure the file exists"
                )
        
        with error_boundary("output directory creation", LayerType.UTILITY):
            try:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                logger.debug(f"Created output directory: {output_path}")
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to create output directory",
                    file_path=output_dir,
                    operation="create_directory",
                    details=f"Directory creation error: {str(e)}",
                    original_exception=e,
                    context={'output_dir': output_dir},
                    suggested_action="Check directory permissions and path validity"
                )
        
        with error_boundary("file content reading", LayerType.UTILITY):
            try:
                with open(input_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if not content.strip():
                    raise DataProcessingException(
                        message="Input file is empty",
                        data_type="file_content",
                        details="File contains no content to process",
                        context={'file_path': input_file, 'file_size': input_path.stat().st_size},
                        suggested_action="Ensure the input file contains markdown content"
                    )
                    
            except UnicodeDecodeError as e:
                raise FileProcessingException(
                    message="Failed to decode file content",
                    file_path=input_file,
                    details=f"Unicode decode error: {str(e)}",
                    original_exception=e,
                    context={'encoding': 'utf-8', 'operation': 'read'},
                    suggested_action="Check file encoding or try a different encoding"
                )
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to read input file",
                    file_path=input_file,
                    details=f"File read error: {str(e)}",
                    original_exception=e,
                    context={'operation': 'read'},
                    suggested_action="Check file permissions and accessibility"
                )
        
        with error_boundary("content formatting", LayerType.UTILITY):
            try:
                if format_numbers:
                    content = self.format_numbers_in_content(content)
                    logger.debug("Applied number formatting to content")
                
                if format_dates:
                    content = self._format_dates_in_content(content)
                    logger.debug("Applied date formatting to content")
                    
            except Exception as e:
                if isinstance(e, (ValidationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to format content",
                    data_type="content_formatting",
                    details=f"Content formatting error: {str(e)}",
                    original_exception=e,
                    context={'format_numbers': format_numbers, 'format_dates': format_dates},
                    suggested_action="Check content format and formatting options"
                )
        
        with error_boundary("content splitting", LayerType.UTILITY):
            try:
                chunks = self.split(content)
                logger.info(f"Split content into {len(chunks)} chunks")
            except Exception as e:
                if isinstance(e, (ValidationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to split content into chunks",
                    data_type="content_splitting",
                    details=f"Content splitting error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content structure and size limits"
                )
        
        with error_boundary("chunk file writing", LayerType.UTILITY):
            try:
                output_files = []
                base_name = input_path.stem
                
                for i, chunk in enumerate(chunks):
                    if not chunk.strip():
                        continue
                        
                    chunk_filename = f"{base_name}_chunk_{i+1:03d}.md"
                    chunk_path = output_path / chunk_filename
                    
                    with open(chunk_path, 'w', encoding='utf-8') as f:
                        f.write(chunk)
                    
                    output_files.append(str(chunk_path))
                    logger.debug(f"Written chunk {i+1} to {chunk_path}")
                
                logger.info(f"Created {len(output_files)} chunk files in {output_dir}")
                return output_files
                
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to write chunk files",
                    file_path=str(output_path),
                    operation="write",
                    details=f"Chunk file writing error: {str(e)}",
                    original_exception=e,
                    context={'chunks_count': len(chunks), 'output_dir': output_dir},
                    suggested_action="Check directory permissions and disk space"
                )

    @log_and_reraise(logger, "date formatting in content")
    def _format_dates_in_content(self, content: str) -> str:
        """Format dates in content to standardized format"""
        set_operation_context("date_formatting")
        
        with error_boundary("date pattern processing", LayerType.UTILITY):
            try:
                # Common date patterns
                date_patterns = [
                    (r'\b(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})[日]?\b', r'\1-\2-\3'),
                    (r'\b(\d{1,2})[月/-](\d{1,2})[日/-](\d{4})\b', r'\3-\1-\2'),
                    (r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b', r'\3-\1-\2')
                ]
                
                formatted_content = content
                for pattern, replacement in date_patterns:
                    formatted_content = re.sub(pattern, replacement, formatted_content)
                
                return formatted_content
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to format dates in content",
                    data_type="date_formatting",
                    details=f"Regex processing error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and date patterns"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Unexpected error during date formatting",
                    data_type="date_formatting",
                    details=f"Date formatting error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content structure and date format"
                )

    @handle_layer_boundary(LayerType.UTILITY, "markdown link removal")
    def remove_links_keep_text(self, content: str) -> str:
        """Remove markdown links but keep the link text"""
        set_operation_context("link_removal")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__},
                    suggested_action="Provide valid string content"
                )
        
        with error_boundary("link pattern processing", LayerType.UTILITY):
            try:
                # Remove markdown links [text](url) -> text
                link_pattern = r'\[([^\]]+)\]\([^)]+\)'
                cleaned_content = re.sub(link_pattern, r'\1', content)
                
                # Remove reference-style links [text][ref] -> text
                ref_link_pattern = r'\[([^\]]+)\]\[[^\]]*\]'
                cleaned_content = re.sub(ref_link_pattern, r'\1', cleaned_content)
                
                # Remove reference definitions [ref]: url
                ref_def_pattern = r'^\s*\[[^\]]+\]:\s*.*$'
                cleaned_content = re.sub(ref_def_pattern, '', cleaned_content, flags=re.MULTILINE)
                
                logger.debug(f"Removed links from content (length={len(content)})")
                return cleaned_content
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to remove links from content",
                    data_type="link_removal",
                    details=f"Regex processing error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and link patterns"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Unexpected error during link removal",
                    data_type="link_removal",
                    details=f"Link removal error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content structure and markdown format"
                )

    @log_and_reraise(logger, "markdown content cleaning")
    def _clean_markdown_content(self, content: str) -> str:
        """Clean and normalize markdown content"""
        set_operation_context("content_cleaning")
        
        with error_boundary("content normalization", LayerType.UTILITY):
            try:
                # Remove excessive whitespace
                cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
                
                # Fix table formatting
                cleaned = re.sub(r'\|\s*\|', '| |', cleaned)
                
                # Normalize line endings
                cleaned = cleaned.replace('\r\n', '\n').replace('\r', '\n')
                
                # Remove trailing whitespace
                lines = [line.rstrip() for line in cleaned.split('\n')]
                cleaned = '\n'.join(lines)
                
                return cleaned.strip()
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to clean markdown content",
                    data_type="content_cleaning",
                    details=f"Content cleaning error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and structure"
                )

    def _check_wkhtmltopdf_installation(self) -> dict:
        """Check wkhtmltopdf installation and return diagnostic information"""
        import subprocess
        import shutil

        diagnostics = {
            'wkhtmltopdf_found': False,
            'wkhtmltopdf_path': None,
            'wkhtmltopdf_version': None,
            'error_details': None
        }

        try:
            # Check if wkhtmltopdf is in PATH
            wkhtmltopdf_path = shutil.which('wkhtmltopdf')
            if wkhtmltopdf_path:
                diagnostics['wkhtmltopdf_found'] = True
                diagnostics['wkhtmltopdf_path'] = wkhtmltopdf_path

                # Get version information
                try:
                    result = subprocess.run(
                        ['wkhtmltopdf', '--version'],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        diagnostics['wkhtmltopdf_version'] = result.stdout.strip()
                    else:
                        diagnostics['error_details'] = f"Version check failed: {result.stderr}"
                except subprocess.TimeoutExpired:
                    diagnostics['error_details'] = "Version check timed out"
                except Exception as e:
                    diagnostics['error_details'] = f"Version check error: {str(e)}"
            else:
                diagnostics['error_details'] = "wkhtmltopdf not found in system PATH"

        except Exception as e:
            diagnostics['error_details'] = f"Installation check failed: {str(e)}"

        return diagnostics

    def _validate_file_access(self, file_path: Path) -> dict:
        """Validate file access and return diagnostic information"""
        import stat

        diagnostics = {
            'file_exists': False,
            'file_readable': False,
            'file_size': 0,
            'file_permissions': None,
            'file_encoding': None,
            'content_preview': None,
            'error_details': []
        }

        try:
            if file_path.exists():
                diagnostics['file_exists'] = True

                # Check file size
                try:
                    diagnostics['file_size'] = file_path.stat().st_size
                except Exception as e:
                    diagnostics['error_details'].append(f"Size check failed: {str(e)}")

                # Check permissions
                try:
                    file_stat = file_path.stat()
                    diagnostics['file_permissions'] = oct(stat.S_IMODE(file_stat.st_mode))
                    diagnostics['file_readable'] = os.access(file_path, os.R_OK)
                except Exception as e:
                    diagnostics['error_details'].append(f"Permission check failed: {str(e)}")

                # Try to read a small sample to check encoding and content
                if diagnostics['file_readable']:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content_sample = f.read(500)  # Read first 500 chars
                            diagnostics['file_encoding'] = 'utf-8'
                            diagnostics['content_preview'] = content_sample[:100] + "..." if len(content_sample) > 100 else content_sample
                    except UnicodeDecodeError:
                        try:
                            with open(file_path, 'r', encoding='latin-1') as f:
                                content_sample = f.read(500)
                                diagnostics['file_encoding'] = 'latin-1'
                                diagnostics['content_preview'] = content_sample[:100] + "..." if len(content_sample) > 100 else content_sample
                        except Exception as e:
                            diagnostics['error_details'].append(f"Encoding detection failed: {str(e)}")
                    except Exception as e:
                        diagnostics['error_details'].append(f"Content read failed: {str(e)}")
            else:
                diagnostics['error_details'].append("File does not exist")

        except Exception as e:
            diagnostics['error_details'].append(f"File validation failed: {str(e)}")

        return diagnostics

    def _validate_output_directory(self, output_path: Path) -> dict:
        """Validate output directory access and return diagnostic information"""
        diagnostics = {
            'directory_exists': False,
            'directory_writable': False,
            'directory_permissions': None,
            'available_space': None,
            'error_details': []
        }

        try:
            output_dir = output_path.parent

            if output_dir.exists():
                diagnostics['directory_exists'] = True

                # Check write permissions
                try:
                    diagnostics['directory_writable'] = os.access(output_dir, os.W_OK)
                except Exception as e:
                    diagnostics['error_details'].append(f"Write permission check failed: {str(e)}")

                # Check directory permissions
                try:
                    import stat
                    dir_stat = output_dir.stat()
                    diagnostics['directory_permissions'] = oct(stat.S_IMODE(dir_stat.st_mode))
                except Exception as e:
                    diagnostics['error_details'].append(f"Permission check failed: {str(e)}")

                # Check available disk space
                try:
                    import shutil
                    total, used, free = shutil.disk_usage(output_dir)
                    diagnostics['available_space'] = {
                        'free_bytes': free,
                        'free_mb': free // (1024 * 1024),
                        'total_bytes': total
                    }
                except Exception as e:
                    diagnostics['error_details'].append(f"Disk space check failed: {str(e)}")
            else:
                diagnostics['error_details'].append(f"Output directory does not exist: {output_dir}")

        except Exception as e:
            diagnostics['error_details'].append(f"Directory validation failed: {str(e)}")

        return diagnostics

    def _analyze_pdfkit_error(self, error: Exception, input_path: Path, output_path: Path, options: dict) -> dict:
        """Analyze pdfkit/wkhtmltopdf error and provide detailed diagnostics"""
        import subprocess
        import re

        error_analysis = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'detailed_error': str(error),
            'suggested_action': "Check wkhtmltopdf installation and configuration",
            'error_category': 'unknown',
            'command_executed': None,
            'stderr_output': None,
            'troubleshooting_steps': []
        }

        try:
            # Extract command information if available
            if hasattr(error, 'args') and error.args:
                error_msg = str(error.args[0]) if error.args else str(error)
                error_analysis['detailed_error'] = error_msg

                # Try to extract the actual command that was executed
                if 'wkhtmltopdf' in error_msg:
                    # Look for command patterns in the error message
                    cmd_match = re.search(r'wkhtmltopdf[^"]*', error_msg)
                    if cmd_match:
                        error_analysis['command_executed'] = cmd_match.group(0)

                # Analyze common error patterns
                if 'exit with code 1' in error_msg.lower():
                    error_analysis['error_category'] = 'wkhtmltopdf_execution_error'
                    error_analysis['suggested_action'] = "wkhtmltopdf execution failed - check input file format and system configuration"
                    error_analysis['troubleshooting_steps'] = [
                        "Verify the input markdown file is valid and readable",
                        "Check if wkhtmltopdf can access the input file",
                        "Ensure output directory has write permissions",
                        "Try running wkhtmltopdf manually with the same parameters"
                    ]
                elif 'no such file or directory' in error_msg.lower():
                    error_analysis['error_category'] = 'file_not_found'
                    error_analysis['suggested_action'] = "wkhtmltopdf executable not found in system PATH"
                    error_analysis['troubleshooting_steps'] = [
                        "Install wkhtmltopdf from https://wkhtmltopdf.org/downloads.html",
                        "Add wkhtmltopdf to system PATH",
                        "Verify installation with: wkhtmltopdf --version"
                    ]
                elif 'permission denied' in error_msg.lower():
                    error_analysis['error_category'] = 'permission_error'
                    error_analysis['suggested_action'] = "Permission denied - check file and directory permissions"
                    error_analysis['troubleshooting_steps'] = [
                        "Check read permissions on input file",
                        "Check write permissions on output directory",
                        "Ensure wkhtmltopdf has execution permissions"
                    ]
                elif 'protocol error' in error_msg.lower() or 'ssl' in error_msg.lower():
                    error_analysis['error_category'] = 'network_error'
                    error_analysis['suggested_action'] = "Network or SSL error - check internet connectivity and certificates"
                    error_analysis['troubleshooting_steps'] = [
                        "Check internet connectivity",
                        "Verify SSL certificates are up to date",
                        "Try with --disable-ssl-verification option"
                    ]
                elif 'content empty' in error_msg.lower() or 'no content' in error_msg.lower():
                    error_analysis['error_category'] = 'empty_content'
                    error_analysis['suggested_action'] = "Input file appears to be empty or contains no renderable content"
                    error_analysis['troubleshooting_steps'] = [
                        "Check that the input markdown file contains valid content",
                        "Verify the file encoding is UTF-8",
                        "Try with a simple test markdown file"
                    ]

                # Try to extract stderr output if available
                if hasattr(error, 'stderr') and error.stderr:
                    error_analysis['stderr_output'] = error.stderr
                elif 'error:' in error_msg:
                    # Extract error details after "error:" marker
                    error_parts = error_msg.split('error:', 1)
                    if len(error_parts) > 1:
                        error_analysis['stderr_output'] = error_parts[1].strip()

            # Try to run wkhtmltopdf directly to get more detailed error information
            try:
                # Construct the command that would be executed
                cmd = ['wkhtmltopdf']
                for key, value in options.items():
                    if value is None:
                        cmd.append(f'--{key}')
                    else:
                        cmd.extend([f'--{key}', str(value)])
                cmd.extend([str(input_path), str(output_path)])

                error_analysis['command_executed'] = ' '.join(cmd)

                # Try to execute and capture detailed error
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if result.stderr:
                    error_analysis['stderr_output'] = result.stderr
                    error_analysis['detailed_error'] += f"\nwkhtmltopdf stderr: {result.stderr}"

                if result.returncode != 0:
                    error_analysis['detailed_error'] += f"\nwkhtmltopdf exit code: {result.returncode}"

            except subprocess.TimeoutExpired:
                error_analysis['troubleshooting_steps'].append("wkhtmltopdf command timed out - try with simpler content or different options")
            except FileNotFoundError:
                error_analysis['error_category'] = 'wkhtmltopdf_not_found'
                error_analysis['suggested_action'] = "wkhtmltopdf executable not found"
            except Exception as subprocess_error:
                error_analysis['troubleshooting_steps'].append(f"Could not run diagnostic command: {str(subprocess_error)}")

        except Exception as analysis_error:
            error_analysis['troubleshooting_steps'].append(f"Error analysis failed: {str(analysis_error)}")

        return error_analysis

    @handle_layer_boundary(LayerType.UTILITY, "PDF conversion")
    def convert_to_pdf(self, markdown_file: str) -> Optional[str]:
        """Convert markdown file to PDF with enhanced error diagnostics"""
        set_operation_context("pdf_conversion")

        with error_boundary("input validation", LayerType.UTILITY):
            if not isinstance(markdown_file, str) or not markdown_file.strip():
                raise ValidationException(
                    message="Markdown file path must be a non-empty string",
                    field_name="markdown_file",
                    details=f"Expected non-empty string, got {type(markdown_file)}",
                    context={'input_type': type(markdown_file).__name__},
                    suggested_action="Provide a valid markdown file path"
                )

        # Enhanced file validation with detailed diagnostics
        with error_boundary("file validation", LayerType.UTILITY):
            md_path = Path(markdown_file)
            file_diagnostics = self._validate_file_access(md_path)

            if not file_diagnostics['file_exists']:
                raise FileProcessingException(
                    message="Markdown file not found",
                    file_path=markdown_file,
                    details=f"File does not exist: {markdown_file}",
                    context={
                        'file_path': markdown_file,
                        'operation': 'read',
                        'file_diagnostics': file_diagnostics
                    },
                    suggested_action="Check the file path and ensure the file exists"
                )

            if not file_diagnostics['file_readable']:
                raise FileProcessingException(
                    message="Markdown file is not readable",
                    file_path=markdown_file,
                    details=f"File exists but cannot be read: {markdown_file}",
                    context={
                        'file_path': markdown_file,
                        'operation': 'read',
                        'file_diagnostics': file_diagnostics
                    },
                    suggested_action="Check file permissions and ensure the file is readable"
                )

        # Enhanced dependency validation with wkhtmltopdf diagnostics
        with error_boundary("PDF conversion dependencies", LayerType.UTILITY):
            try:
                import pdfkit  # Lazy import
            except ImportError as e:
                raise ConfigurationException(
                    message="PDF conversion dependencies not available",
                    details="pdfkit not installed",
                    original_exception=e,
                    config_key="pdfkit",
                    context={'missing_package': 'pdfkit'},
                    suggested_action="Install dependencies with: pip install pdfkit"
                )

            # Check wkhtmltopdf installation
            wkhtmltopdf_diagnostics = self._check_wkhtmltopdf_installation()
            if not wkhtmltopdf_diagnostics['wkhtmltopdf_found']:
                raise ConfigurationException(
                    message="wkhtmltopdf not found",
                    details="wkhtmltopdf is required for PDF conversion but was not found on the system",
                    config_key="wkhtmltopdf",
                    context={
                        'wkhtmltopdf_diagnostics': wkhtmltopdf_diagnostics,
                        'system_path': os.environ.get('PATH', 'Not available')
                    },
                    suggested_action="Install wkhtmltopdf: https://wkhtmltopdf.org/downloads.html or use package manager (e.g., apt install wkhtmltopdf, brew install wkhtmltopdf)"
                )

        # Enhanced PDF conversion with detailed error capture
        with error_boundary("PDF conversion process", LayerType.UTILITY):
            pdf_path = md_path.with_suffix('.pdf')

            # Validate output directory
            output_diagnostics = self._validate_output_directory(pdf_path)
            if not output_diagnostics['directory_writable']:
                raise FileProcessingException(
                    message="Cannot write to output directory",
                    file_path=str(pdf_path.parent),
                    details=f"Output directory is not writable: {pdf_path.parent}",
                    context={
                        'output_path': str(pdf_path),
                        'operation': 'write',
                        'output_diagnostics': output_diagnostics
                    },
                    suggested_action="Check directory permissions and ensure write access to the output location"
                )

            try:
                # Configure PDF options
                options = {
                    'page-size': 'A4',
                    'margin-top': '0.75in',
                    'margin-right': '0.75in',
                    'margin-bottom': '0.75in',
                    'margin-left': '0.75in',
                    'encoding': "UTF-8",
                    'no-outline': None,
                    'enable-local-file-access': None  # Allow local file access
                }

                # Merge user-provided options if available
                if hasattr(self, 'pdf_options') and self.pdf_options:
                    options.update(self.pdf_options)

                logger.debug(f"Converting {md_path} to PDF with options: {options}")

                # First convert markdown to HTML
                with error_boundary("markdown to HTML conversion", LayerType.UTILITY):
                    try:
                        import markdown2

                        # Read markdown content
                        with open(md_path, 'r', encoding='utf-8') as f:
                            md_content = f.read()

                        # Convert markdown to HTML with enhanced features
                        html_content = markdown2.markdown(
                            md_content,
                            extras=[
                                'tables',
                                'fenced-code-blocks',
                                'header-ids',
                                'footnotes',
                                'strike',
                                'task_list'
                            ]
                        )

                        # Create a complete HTML document
                        full_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Document</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        code {{
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
        }}
        pre {{
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }}
    </style>
</head>
<body>
{html_content}
</body>
</html>"""

                        logger.debug(f"Converted markdown to HTML ({len(html_content)} chars)")

                    except UnicodeDecodeError as e:
                        raise FileProcessingException(
                            message="Failed to read markdown file",
                            file_path=str(md_path),
                            details=f"Unicode decode error: {str(e)}",
                            original_exception=e,
                            context={
                                'operation': 'read',
                                'encoding_attempted': 'utf-8',
                                'file_diagnostics': file_diagnostics
                            },
                            suggested_action="Check file encoding - try saving the file as UTF-8"
                        )
                    except Exception as e:
                        raise FileProcessingException(
                            message="Failed to convert markdown to HTML",
                            file_path=str(md_path),
                            details=f"Markdown conversion error: {str(e)}",
                            original_exception=e,
                            context={
                                'operation': 'markdown_to_html',
                                'file_diagnostics': file_diagnostics
                            },
                            suggested_action="Check markdown file format and content"
                        )

                # Convert HTML to PDF with enhanced error capture
                try:
                    pdfkit.from_string(full_html, str(pdf_path), options=options)
                except Exception as pdfkit_error:
                    # Capture detailed error information
                    error_context = self._analyze_pdfkit_error(pdfkit_error, md_path, pdf_path, options)
                    raise FileProcessingException(
                        message="PDF conversion failed",
                        file_path=markdown_file,
                        details=error_context['detailed_error'],
                        original_exception=pdfkit_error,
                        context={
                            'operation': 'convert',
                            'input_file': str(md_path),
                            'output_file': str(pdf_path),
                            'pdf_options': options,
                            'file_diagnostics': file_diagnostics,
                            'output_diagnostics': output_diagnostics,
                            'wkhtmltopdf_diagnostics': wkhtmltopdf_diagnostics,
                            'error_analysis': error_context
                        },
                        suggested_action=error_context['suggested_action']
                    )

                # Verify the output file was created successfully
                if not pdf_path.exists():
                    raise FileProcessingException(
                        message="PDF file was not created",
                        file_path=str(pdf_path),
                        details="wkhtmltopdf completed without error but no output file was generated",
                        context={
                            'operation': 'convert',
                            'input_file': str(md_path),
                            'output_file': str(pdf_path),
                            'pdf_options': options,
                            'output_diagnostics': output_diagnostics
                        },
                        suggested_action="Check wkhtmltopdf configuration and output directory permissions"
                    )

                # Verify the output file has content
                if pdf_path.stat().st_size == 0:
                    raise FileProcessingException(
                        message="PDF file is empty",
                        file_path=str(pdf_path),
                        details="PDF file was created but contains no content",
                        context={
                            'operation': 'convert',
                            'input_file': str(md_path),
                            'output_file': str(pdf_path),
                            'file_size': 0,
                            'pdf_options': options
                        },
                        suggested_action="Check input file content and wkhtmltopdf configuration"
                    )

                logger.info(f"Successfully converted markdown to PDF: {pdf_path} ({pdf_path.stat().st_size} bytes)")
                return str(pdf_path)

            except FileProcessingException:
                # Re-raise our custom exceptions
                raise
            except Exception as e:
                # Handle any other unexpected errors
                error_context = {
                    'operation': 'convert',
                    'input_file': str(md_path),
                    'output_file': str(pdf_path) if 'pdf_path' in locals() else 'unknown',
                    'file_diagnostics': file_diagnostics,
                    'wkhtmltopdf_diagnostics': wkhtmltopdf_diagnostics,
                    'unexpected_error': True
                }

                raise FileProcessingException(
                    message="Unexpected error during PDF conversion",
                    file_path=markdown_file,
                    details=f"Unexpected error: {str(e)}",
                    original_exception=e,
                    context=error_context,
                    suggested_action="Check system configuration and try again. If the problem persists, contact support."
                )

    @handle_layer_boundary(LayerType.UTILITY, "DOCX conversion")
    def convert_to_docx(self, markdown_file: str) -> Optional[str]:
        """Convert markdown file to DOCX with formatting"""
        set_operation_context("docx_conversion")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not isinstance(markdown_file, str) or not markdown_file.strip():
                raise ValidationException(
                    message="Markdown file path must be a non-empty string",
                    field_name="markdown_file",
                    details=f"Expected non-empty string, got {type(markdown_file)}",
                    context={'input_type': type(markdown_file).__name__},
                    suggested_action="Provide a valid markdown file path"
                )
        
        with error_boundary("file existence check", LayerType.UTILITY):
            md_path = Path(markdown_file)
            if not md_path.exists():
                raise FileProcessingException(
                    message="Markdown file not found",
                    file_path=markdown_file,
                    operation="read",
                    details=f"File does not exist: {markdown_file}",
                    context={'file_path': markdown_file},
                    suggested_action="Check the file path and ensure the file exists"
                )
        
        with error_boundary("DOCX conversion dependencies", LayerType.UTILITY):
            try:
                from docx import Document  # Lazy import
                import markdown2
            except ImportError as e:
                logger.error("python-docx or markdown2 not installed, falling back to PDF-based conversion")
                return self._convert_to_docx_via_pdf(markdown_file)
        
        with error_boundary("markdown content reading", LayerType.UTILITY):
            try:
                with open(md_path, 'r', encoding='utf-8') as f:
                    md_content = f.read()
                    
                if not md_content.strip():
                    raise DataProcessingException(
                        message="Markdown file is empty",
                        data_type="file_content",
                        details="File contains no content to convert",
                        context={'file_path': markdown_file},
                        suggested_action="Ensure the markdown file contains content"
                    )
                    
            except UnicodeDecodeError as e:
                raise FileProcessingException(
                    message="Failed to decode markdown file",
                    file_path=markdown_file,
                    details=f"Unicode decode error: {str(e)}",
                    original_exception=e,
                    context={'encoding': 'utf-8', 'operation': 'read'},
                    suggested_action="Check file encoding"
                )
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to read markdown file",
                    file_path=markdown_file,
                    details=f"File read error: {str(e)}",
                    original_exception=e,
                    context={'operation': 'read'},
                    suggested_action="Check file permissions and accessibility"
                )
        
        with error_boundary("DOCX document creation", LayerType.UTILITY):
            try:
                # Convert markdown to HTML
                html_content = markdown2.markdown(md_content, extras=['tables', 'fenced-code-blocks'])
                
                # Create DOCX document
                doc = Document()
                docx_path = md_path.with_suffix('.docx')
                
                # Add content to document (simplified conversion)
                # This is a basic implementation - could be enhanced with proper HTML parsing
                paragraphs = md_content.split('\n\n')
                for para in paragraphs:
                    if para.strip():
                        doc.add_paragraph(para.strip())
                
                # Save the document
                doc.save(str(docx_path))
                logger.info(f"Created formatted DOCX file: {docx_path}")
                
                return str(docx_path)
                
            except Exception as e:
                logger.error(f"DOCX conversion failed: {str(e)}")
                return self._convert_to_docx_via_pdf(markdown_file)

    @log_and_reraise(logger, "DOCX conversion via PDF")
    def _convert_to_docx_via_pdf(self, markdown_file: str) -> Optional[str]:
        """Fallback DOCX conversion via PDF"""
        set_operation_context("docx_via_pdf_conversion")
        
        with error_boundary("PDF intermediate conversion", LayerType.UTILITY):
            try:
                pdf_path = self.convert_to_pdf(markdown_file)
                if not pdf_path:
                    return None
                    
                # Note: This would require additional PDF to DOCX conversion
                # For now, return the PDF path as fallback
                logger.warning("DOCX conversion via PDF not fully implemented, returning PDF")
                return pdf_path
                
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to convert to DOCX via PDF",
                    file_path=markdown_file,
                    details=f"PDF intermediate conversion error: {str(e)}",
                    original_exception=e,
                    context={'operation': 'convert'},
                    suggested_action="Check PDF conversion dependencies"
                )

    # --------------------------
    # Command Methods
    # --------------------------

    @handle_layer_boundary(LayerType.UTILITY, "QCC markdown cleaning")
    def command_clean_qcc(self, file_path: str) -> str:
        """Clean QCC-specific markdown content"""
        set_operation_context("qcc_cleaning")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not isinstance(file_path, str) or not file_path.strip():
                raise ValidationException(
                    message="File path must be a non-empty string",
                    field_name="file_path",
                    details=f"Expected non-empty string, got {type(file_path)}",
                    context={'input_type': type(file_path).__name__},
                    suggested_action="Provide a valid file path"
                )
        
        with error_boundary("file processing", LayerType.UTILITY):
            try:
                file_path_obj = Path(file_path)
                if not file_path_obj.exists():
                    raise FileProcessingException(
                        message="File not found",
                        file_path=file_path,
                        operation="read",
                        details=f"File does not exist: {file_path}",
                        suggested_action="Check the file path and ensure the file exists"
                    )
                
                with open(file_path_obj, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Apply QCC-specific cleaning
                cleaned_content = self._clean_markdown_content(content)
                cleaned_content = self.remove_links_keep_text(cleaned_content)
                
                # Write back cleaned content
                with open(file_path_obj, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                logger.info(f"Cleaned QCC markdown file: {file_path}")
                return str(file_path_obj)
                
            except Exception as e:
                if isinstance(e, (ValidationException, FileProcessingException, DataProcessingException)):
                    raise
                raise FileProcessingException(
                    message="Failed to clean QCC markdown file",
                    file_path=file_path,
                    details=f"QCC cleaning error: {str(e)}",
                    original_exception=e,
                    context={'operation': 'process'},
                    suggested_action="Check file format and permissions"
                )

    @handle_layer_boundary(LayerType.UTILITY, "document processing from files")
    def command_from_doc(self, input_files: List[str], output_dir: Optional[str] = None) -> List[str]:
        """Process documents (Excel/PDF) into markdown chunks"""
        set_operation_context("document_processing")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not input_files:
                raise ValidationException(
                    message="Input files list cannot be empty",
                    field_name="input_files",
                    details="No input files provided",
                    context={'input_files_count': len(input_files) if input_files else 0},
                    suggested_action="Provide a list of input files to process"
                )
            
            if not isinstance(input_files, list):
                raise ValidationException(
                    message="Input files must be a list",
                    field_name="input_files",
                    details=f"Expected list, got {type(input_files)}",
                    context={'input_type': type(input_files).__name__},
                    suggested_action="Provide a valid list of file paths"
                )
        
        with error_boundary("output directory setup", LayerType.UTILITY):
            if output_dir is None:
                first_file = Path(input_files[0])
                output_dir = str(first_file.parent)
                logger.debug(f"Using output directory from first file: {output_dir}")
            
            try:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to create output directory",
                    file_path=output_dir,
                    operation="create_directory",
                    details=f"Directory creation error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check directory permissions and path validity"
                )
        
        logger.info(f"Processing {len(input_files)} documents to markdown")
        all_md_files = []
        processed_files = set()
        
        with error_boundary("file collection and processing", LayerType.UTILITY):
            try:
                # Collect all files to process
                files_to_process = []
                for input_item in input_files:
                    input_path = Path(input_item)
                    
                    if input_path.is_dir():
                        # Scan directory for supported files
                        for pattern in ['*.xls', '*.xlsx', '*.pdf']:
                            files_to_process.extend(input_path.glob(pattern))
                    elif input_path.exists():
                        files_to_process.append(input_path)
                    else:
                        # Try glob pattern
                        import glob
                        matched_files = glob.glob(input_item)
                        files_to_process.extend([Path(f) for f in matched_files])
                
                if not files_to_process:
                    raise ValidationException(
                        message="No valid files found to process",
                        field_name="input_files",
                        details="No Excel or PDF files found in the specified paths",
                        context={'input_files': input_files, 'searched_patterns': ['*.xls', '*.xlsx', '*.pdf']},
                        suggested_action="Check file paths and ensure files exist"
                    )
                
                logger.info(f"Found {len(files_to_process)} files to process")
                
                # Process each file
                for file_path in files_to_process:
                    if str(file_path) in processed_files:
                        continue
                    
                    try:
                        if file_path.suffix.lower() in ['.xls', '.xlsx']:
                            md_files = self._process_excel_file(file_path, output_dir)
                        elif file_path.suffix.lower() == '.pdf':
                            md_files = self._process_pdf_file(file_path, output_dir)
                        else:
                            logger.warning(f"Unsupported file type: {file_path}")
                            continue
                        
                        all_md_files.extend(md_files)
                        processed_files.add(str(file_path))
                        logger.info(f"Processed {file_path} -> {len(md_files)} markdown files")
                        
                    except Exception as e:
                        logger.error(f"Failed to process {file_path}: {str(e)}")
                        # Continue processing other files
                        continue
                
                return all_md_files
                
            except Exception as e:
                if isinstance(e, (ValidationException, FileProcessingException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to process document files",
                    data_type="document_processing",
                    details=f"Document processing error: {str(e)}",
                    original_exception=e,
                    context={'input_files_count': len(input_files), 'processed_count': len(processed_files)},
                    suggested_action="Check file formats and processing dependencies"
                )

    @log_and_reraise(logger, "Excel file processing")
    def _process_excel_file(self, file_path: Path, output_dir: str) -> List[str]:
        """Process Excel file to markdown chunks"""
        set_operation_context("excel_file_processing")
        
        with error_boundary("Excel conversion dependencies", LayerType.UTILITY):
            try:
                from markitdown import MarkItDown  # Lazy import
            except ImportError as e:
                raise ConfigurationException(
                    message="Excel processing dependencies not available",
                    details="markitdown not installed",
                    original_exception=e,
                    config_key="markitdown",
                    context={'missing_package': 'markitdown'},
                    suggested_action="Install dependencies with: pip install markitdown"
                )
        
        with error_boundary("Excel to markdown conversion", LayerType.UTILITY):
            try:
                mid = MarkItDown(enable_plugins=False)
                result = mid.convert(str(file_path))
                
                if not result.text_content:
                    raise DataProcessingException(
                        message="Excel conversion produced no content",
                        data_type="excel_conversion",
                        details="Converted content is empty",
                        context={'file_path': str(file_path)},
                        suggested_action="Check Excel file content and format"
                    )
                
                # Clean and process content
                fixed_content = self._clean_markdown_content(result.text_content)
                clean_path = Path(output_dir) / f"{file_path.stem}.md"
                
                with open(clean_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                # Split into chunks
                chunks = self.split_by_tables(str(clean_path), output_dir, format_dates=True, format_numbers=True)
                
                # Clean up intermediate file
                clean_path.unlink()
                
                logger.debug(f"Converted Excel to {len(chunks)} markdown chunks")
                return chunks
                
            except Exception as e:
                if isinstance(e, (ConfigurationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to process Excel file",
                    data_type="excel_processing",
                    details=f"Excel processing error: {str(e)}",
                    original_exception=e,
                    context={'file_path': str(file_path)},
                    suggested_action="Check Excel file format and content"
                )

    @log_and_reraise(logger, "PDF file processing")
    def _process_pdf_file(self, file_path: Path, output_dir: str) -> List[str]:
        """Process PDF file to markdown chunks"""
        set_operation_context("pdf_file_processing")
        
        with error_boundary("PDF conversion dependencies", LayerType.UTILITY):
            try:
                from marker.output import text_from_rendered  # Lazy import
            except ImportError as e:
                raise ConfigurationException(
                    message="PDF processing dependencies not available",
                    details="marker not installed",
                    original_exception=e,
                    config_key="marker",
                    context={'missing_package': 'marker'},
                    suggested_action="Install dependencies with: pip install marker-pdf"
                )
        
        with error_boundary("PDF to markdown conversion", LayerType.UTILITY):
            try:
                pdf_converter = self._get_pdf_converter()
                if not pdf_converter:
                    raise ConfigurationException(
                        message="PDF converter required for PDF processing",
                        details="PDF converter initialization failed",
                        context={'file_path': str(file_path)},
                        suggested_action="Check PDF converter configuration and dependencies"
                    )
                
                logger.debug(f"Converting PDF to markdown: {file_path}")
                rendered = pdf_converter(str(file_path))
                text, _, _ = text_from_rendered(rendered)
                
                if not text:
                    raise DataProcessingException(
                        message="PDF conversion produced no content",
                        data_type="pdf_conversion",
                        details="Converted content is empty",
                        context={'file_path': str(file_path)},
                        suggested_action="Check PDF file content and format"
                    )
                
                # Clean and process content
                fixed_content = self._clean_markdown_content(text)
                clean_content = self.remove_links_keep_text(fixed_content)
                clean_path = Path(output_dir) / f"{file_path.stem}.md"
                
                with open(clean_path, 'w', encoding='utf-8') as f:
                    f.write(clean_content)
                
                # Split into chunks
                chunks = self.split_by_tables(str(clean_path), output_dir, format_dates=True, format_numbers=True)
                
                # Clean up intermediate file
                clean_path.unlink()
                
                logger.debug(f"Converted PDF to {len(chunks)} markdown chunks")
                return chunks
                
            except Exception as e:
                if isinstance(e, (ConfigurationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to process PDF file",
                    data_type="pdf_processing",
                    details=f"PDF processing error: {str(e)}",
                    original_exception=e,
                    context={'file_path': str(file_path)},
                    suggested_action="Check PDF file format and content"
                )

    @handle_layer_boundary(LayerType.UTILITY, "RAGFlow upload")
    def command_ragflow_upload(self, files: Optional[List[str]] = None,
                             directory: Optional[str] = None,
                             company_md: Optional[str] = None) -> dict:
        """Upload files to RAGFlow dataset with unified error handling"""
        set_operation_context("ragflow_upload")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not files and not directory:
                raise ValidationException(
                    message="Either files or directory must be provided",
                    field_name="files_or_directory",
                    details="Both files and directory parameters are None",
                    context={'files_provided': bool(files), 'directory_provided': bool(directory)},
                    suggested_action="Provide either a list of files or a directory path"
                )
        
        with error_boundary("RAGFlow dependencies", LayerType.UTILITY):
            try:
                from ragflow_api.client import RAGFlowClient  # Lazy import
                import time
                from datetime import datetime
                import os
            except ImportError as e:
                raise ConfigurationException(
                    message="RAGFlow dependencies not available",
                    details="ragflow_api not installed",
                    original_exception=e,
                    config_key="ragflow_api",
                    context={'missing_package': 'ragflow_api'},
                    suggested_action="Install dependencies with: pip install ragflow-api"
                )
        
        with error_boundary("environment configuration", LayerType.UTILITY):
            base_url = os.getenv('RAGFLOW_BASE_URL')
            api_key = os.getenv('RAGFLOW_API_KEY')
            
            if not base_url:
                raise ConfigurationException(
                    message="RAGFlow base URL not configured",
                    details="RAGFLOW_BASE_URL environment variable not set",
                    config_key="RAGFLOW_BASE_URL",
                    context={'env_vars_checked': ['RAGFLOW_BASE_URL']},
                    suggested_action="Set RAGFLOW_BASE_URL environment variable"
                )
            
            if not api_key:
                raise ConfigurationException(
                    message="RAGFlow API key not configured",
                    details="RAGFLOW_API_KEY environment variable not set",
                    config_key="RAGFLOW_API_KEY",
                    context={'env_vars_checked': ['RAGFLOW_API_KEY']},
                    suggested_action="Set RAGFLOW_API_KEY environment variable"
                )
        
        logger.info("Starting RAGFlow upload process")
        start_time = time.time()
        
        with error_boundary("file collection", LayerType.UTILITY):
            try:
                upload_files = set()
                
                # Collect files from directory
                if directory and os.path.exists(directory):
                    for root, dirs, filenames in os.walk(directory):
                        for filename in filenames:
                            if filename.endswith(('.md', '.pdf', '.txt', '.docx')):
                                upload_files.add(os.path.join(root, filename))
                
                # Add individual files
                if files:
                    for file_path in files:
                        if os.path.exists(file_path):
                            upload_files.add(file_path)
                        else:
                            logger.warning(f"File not found: {file_path}")
                
                if not upload_files:
                    raise ValidationException(
                        message="No valid files found for upload",
                        field_name="upload_files",
                        details="No supported files found in specified paths",
                        context={'files_provided': bool(files), 'directory_provided': bool(directory)},
                        suggested_action="Check file paths and ensure supported files exist"
                    )
                
                logger.info(f"Found {len(upload_files)} files for upload")
                
            except Exception as e:
                if isinstance(e, ValidationException):
                    raise
                raise DataProcessingException(
                    message="Failed to collect files for upload",
                    data_type="file_collection",
                    details=f"File collection error: {str(e)}",
                    original_exception=e,
                    context={'directory': directory, 'files_count': len(files) if files else 0},
                    suggested_action="Check file paths and directory permissions"
                )
        
        dataset_name = 'dataset'
        
        with error_boundary("company markdown processing", LayerType.UTILITY):
            if company_md and os.path.exists(company_md):
                try:
                    logger.info(f"Processing company markdown file: {company_md}")
                    dataset_name = Path(company_md).stem
                    logger.debug(f"Using company name for dataset: {dataset_name}")
                    
                    # Clean company markdown
                    self.command_clean_qcc(company_md)
                    
                    # Convert to PDF
                    pdf_path = self.convert_to_pdf(company_md)
                    if pdf_path and os.path.exists(pdf_path):
                        upload_files.add(pdf_path)
                        logger.info(f"Added company PDF to upload: {pdf_path}")
                    else:
                        logger.warning("Failed to convert company markdown to PDF")
                        
                except Exception as e:
                    if isinstance(e, (ValidationException, FileProcessingException, DataProcessingException)):
                        raise
                    raise DataProcessingException(
                        message="Failed to process company markdown",
                        data_type="company_markdown_processing",
                        details=f"Company markdown processing error: {str(e)}",
                        original_exception=e,
                        context={'company_md': company_md},
                        suggested_action="Check company markdown file format and content"
                    )
        
        with error_boundary("RAGFlow upload process", LayerType.UTILITY):
            try:
                # Initialize RAGFlow client
                client = RAGFlowClient(base_url=base_url, api_key=api_key)
                
                # Create or get dataset
                datasets = client.list_datasets()
                dataset = None
                for ds in datasets:
                    if ds.name == dataset_name:
                        dataset = ds
                        break
                
                if not dataset:
                    dataset = client.create_dataset(name=dataset_name)
                    logger.info(f"Created new dataset: {dataset_name}")
                else:
                    logger.info(f"Using existing dataset: {dataset_name}")
                
                # Upload files
                upload_results = []
                for file_path in upload_files:
                    try:
                        result = client.upload_file(dataset.id, file_path)
                        upload_results.append({
                            'file': file_path,
                            'status': 'success',
                            'result': result
                        })
                        logger.info(f"Uploaded: {file_path}")
                    except Exception as e:
                        upload_results.append({
                            'file': file_path,
                            'status': 'failed',
                            'error': str(e)
                        })
                        logger.error(f"Failed to upload {file_path}: {str(e)}")
                
                end_time = time.time()
                duration = end_time - start_time
                
                summary = {
                    'dataset_name': dataset_name,
                    'total_files': len(upload_files),
                    'successful_uploads': len([r for r in upload_results if r['status'] == 'success']),
                    'failed_uploads': len([r for r in upload_results if r['status'] == 'failed']),
                    'duration_seconds': duration,
                    'upload_results': upload_results
                }
                
                logger.info(f"RAGFlow upload completed: {summary['successful_uploads']}/{summary['total_files']} files uploaded in {duration:.2f}s")
                return summary
                
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to upload files to RAGFlow",
                    data_type="ragflow_upload",
                    details=f"RAGFlow upload error: {str(e)}",
                    original_exception=e,
                    context={'dataset_name': dataset_name, 'files_count': len(upload_files)},
                    suggested_action="Check RAGFlow configuration and network connectivity"
                )

    # --------------------------
    # Command Methods
    # --------------------------

    @handle_layer_boundary(LayerType.UTILITY, "markdown file splitting")
    def command_split(self, input_file: str, output_dir: str, size_limit: int = 10000) -> List[str]:
        """Execute split by headers command with unified error handling"""
        set_operation_context("markdown_file_splitting")
        
        with error_boundary("parameter validation", LayerType.UTILITY):
            if not input_file:
                raise ValidationException(
                    message="Input file path cannot be empty",
                    field_name="input_file",
                    details="Empty input file path provided",
                    context={'input_file': input_file},
                    suggested_action="Provide a valid file path"
                )
            
            if not output_dir:
                raise ValidationException(
                    message="Output directory cannot be empty",
                    field_name="output_dir",
                    details="Empty output directory provided",
                    context={'output_dir': output_dir},
                    suggested_action="Provide a valid output directory path"
                )
            
            if size_limit <= 0:
                raise ValidationException(
                    message="Size limit must be positive",
                    field_name="size_limit",
                    details=f"Size limit must be greater than 0, got: {size_limit}",
                    context={'provided_size_limit': size_limit},
                    suggested_action="Provide a positive size limit value"
                )
        
        logger.info(f"Splitting markdown file: {input_file}")
        logger.debug(f"Output directory: {output_dir}, size_limit: {size_limit}")
        
        with error_boundary("file operations", LayerType.UTILITY):
            try:
                input_path = Path(input_file)
                if not input_path.exists():
                    raise FileProcessingException(
                        message="Input file does not exist",
                        file_path=input_file,
                        details=f"File not found: {input_file}",
                        context={'file_path': str(input_path.absolute())},
                        suggested_action="Check if the file exists and path is correct"
                    )
                
                if not input_path.is_file():
                    raise FileProcessingException(
                        message="Input path is not a file",
                        file_path=input_file,
                        details=f"Path is not a regular file: {input_file}",
                        context={'file_type': 'directory' if input_path.is_dir() else 'other'},
                        suggested_action="Provide a path to a regular file"
                    )
                
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                logger.debug(f"Created output directory: {output_path}")
                
            except OSError as e:
                raise FileProcessingException(
                    message="Failed to access file or directory",
                    file_path=input_file,
                    details=f"File system error: {str(e)}",
                    original_exception=e,
                    context={'input_file': input_file, 'output_dir': output_dir},
                    suggested_action="Check file permissions and disk space"
                )
        
        with error_boundary("content reading", LayerType.UTILITY):
            try:
                with open(input_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                logger.debug(f"Read {len(content)} characters from {input_file}")
            except UnicodeDecodeError as e:
                raise FileProcessingException(
                    message="Failed to decode file content",
                    file_path=input_file,
                    details=f"File encoding error: {str(e)}",
                    original_exception=e,
                    context={'encoding': 'utf-8'},
                    suggested_action="Check file encoding or specify correct encoding"
                )
            except IOError as e:
                raise FileProcessingException(
                    message="Failed to read file content",
                    file_path=input_file,
                    details=f"File I/O error: {str(e)}",
                    original_exception=e,
                    context={'file_path': input_file},
                    suggested_action="Check file permissions and disk space"
                )
        
        with error_boundary("content splitting", LayerType.UTILITY):
            try:
                self.size_limit = size_limit
                chunks = self.split(content)
                logger.debug(f"Split content into {len(chunks)} chunks")
            except Exception as e:
                if isinstance(e, (ValidationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to split markdown content",
                    data_type="markdown_splitting",
                    details=f"Content splitting error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'size_limit': size_limit},
                    suggested_action="Check content structure and size limits"
                )
        
        with error_boundary("chunk processing and writing", LayerType.UTILITY):
            try:
                base_name = input_path.stem
                non_empty_chunks = [chunk for chunk in chunks if chunk.strip()]
                output_files = []
                
                for i, chunk in enumerate(non_empty_chunks, 1):
                    output_file_path = output_path / f'{base_name}_part{i}.md'
                    
                    # Clean and fix content
                    fixed_content = re.sub(r'NaN|Unnamed: \d+', r'', chunk)
                    fixed_content = re.sub(r'\{2}', r'\\', fixed_content)
                    fixed_content = re.sub(r'\\.', r'.', fixed_content)
                    fixed_content = re.sub(r'\\-', r'-', fixed_content)
                    
                    with open(output_file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    
                    logger.info(f'Wrote chunk to: {output_file_path}')
                    output_files.append(str(output_file_path))
                
                logger.info(f"Successfully created {len(output_files)} chunks from {input_file}")
                return output_files
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to apply content cleaning patterns",
                    data_type="content_cleaning",
                    details=f"Regex pattern error: {str(e)}",
                    original_exception=e,
                    context={'chunk_count': len(non_empty_chunks) if 'non_empty_chunks' in locals() else 0},
                    suggested_action="Check regex pattern syntax"
                )
            except IOError as e:
                raise FileProcessingException(
                    message="Failed to write output files",
                    file_path=str(output_path),
                    details=f"File I/O error: {str(e)}",
                    original_exception=e,
                    context={'output_dir': str(output_path), 'expected_files': len(non_empty_chunks) if 'non_empty_chunks' in locals() else 0},
                    suggested_action="Check output directory permissions and disk space"
                )

    def command_split_by_tables(self, input_file: str, output_dir: str, 
                              format_dates: bool = False, 
                              format_numbers: bool = False) -> List[str]:
        """Execute split by tables command"""
        self.input_file = input_file
        self.output_dir = output_dir
        
        return self.split_by_tables(input_file, output_dir, format_dates, format_numbers)

    def command_merge(self, input_files: List[str], output_file: Optional[str] = None) -> str:
        """Execute merge command"""
        chunks = []
        for file_path in input_files:
            with open(file_path, 'r', encoding='utf-8') as f:
                chunks.append(f.read())
        
        merged_content = self.merge_chunks(chunks)
        
        # Derive output filename from input pattern if not specified
        if not output_file:
            first_file = input_files[0]
            base_name = re.sub(r'_part\d+\.md$', '.md', first_file)
            output_file = str(Path(first_file).parent / Path(base_name).name)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(merged_content)
            
        logger.info(f'Merged {len(input_files)} files into {output_file}')
        return output_file

    def command_convert_to_pdf(self, markdown_file: str, pdf_options: Optional[dict] = None) -> Optional[str]:
        """Execute convert to PDF command"""
        self.pdf_options = pdf_options
        return self.convert_to_pdf(markdown_file)

    def remove_header_or_logout(self, content: str) -> str:
        """Remove content before header or logout text"""
        # Try to find header first
        header_pos = content.find('\n# ')
        if header_pos != -1:
            return content[header_pos+1:]  # Keep the header line
        
        # Fallback to logout text
        logout_pos = content.find('__退出登录')
        if logout_pos != -1:
            return content[logout_pos:]  # Keep from logout text
        
        return content

    def remove_links_keep_text(self, content: str) -> str:
        """Remove markdown links and images but keep the text"""
        # Remove image links ![alt](url)
        content = re.sub(r'!\[[^\]]*\]\([^\)]+\)', '', content)
        # Remove normal links [text](url)
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
        return content

    def remove_related_companies(self, content: str) -> str:
        """Remove content starting from '### 相关企业' section"""
        pos = content.find('### 相关企业')
        if pos != -1:
            logger.debug("Found related companies section at position: %d", pos)
            return content[:pos]
        logger.debug("No related companies section found")
        return content

    def remove_underscore_tags(self, content: str) -> str:
        """Remove __ prefix tags and '复制' text"""
        # Remove __ patterns but preserve newlines
        content = re.sub(r'__+[^\S\n]*\S*[^\S\n]*', '', content)  # Remove __ tags without affecting newlines
        # Remove '复制' text
        content = re.sub(r'(复制)|(展开)', '', content)
        # Normalize spaces but keep newlines
        content = re.sub(r'[^\S\n]+', ' ', content)  # Replace multiple spaces with single space
        return content.strip()

    @handle_layer_boundary(LayerType.UTILITY, "qcc_markdown_cleaning")
    def command_clean_qcc(self, input_file: str, output_file: Optional[str] = None) -> str:
        """Clean qichacha markdown file content with comprehensive error handling"""
        set_operation_context("qcc_markdown_cleaning")
        
        # Input validation
        with error_boundary("input validation", LayerType.UTILITY):
            if not input_file:
                raise ValidationException(
                    message="Input file path cannot be empty",
                    field_name="input_file",
                    details="Input file parameter is empty or None",
                    context={'provided_input_file': str(input_file)},
                    suggested_action="Provide a valid file path"
                )
            
            input_path = Path(input_file)
            if not input_path.exists():
                raise FileProcessingException(
                    message="Input file does not exist",
                    file_path=str(input_path),
                    details=f"File not found: {input_path}",
                    context={'file_path': str(input_path), 'absolute_path': str(input_path.absolute())},
                    suggested_action="Verify the file path exists and is accessible"
                )
            
            if not input_path.is_file():
                raise FileProcessingException(
                    message="Input path is not a file",
                    file_path=str(input_path),
                    details=f"Path is not a regular file: {input_path}",
                    context={'file_path': str(input_path), 'file_type': 'directory' if input_path.is_dir() else 'other'},
                    suggested_action="Provide a path to a regular file"
                )
        
        # File reading
        with error_boundary("file reading", LayerType.UTILITY):
            try:
                with open(input_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                logger.debug("Read %d chars from input", len(content))
                
                if not content.strip():
                    raise ValidationException(
                        message="Input file is empty",
                        field_name="content",
                        details="File contains no readable content",
                        context={'file_path': str(input_path), 'file_size': input_path.stat().st_size},
                        suggested_action="Check if the file contains valid content"
                    )
                    
            except UnicodeDecodeError as e:
                raise FileProcessingException(
                    message="Failed to decode file content",
                    file_path=str(input_path),
                    details=f"File encoding error: {str(e)}",
                    original_exception=e,
                    context={'file_path': str(input_path), 'encoding': 'utf-8'},
                    suggested_action="Check file encoding or convert to UTF-8"
                )
            except PermissionError as e:
                raise FileProcessingException(
                    message="Permission denied reading file",
                    file_path=str(input_path),
                    details=f"File access denied: {str(e)}",
                    original_exception=e,
                    context={'file_path': str(input_path)},
                    suggested_action="Check file permissions"
                )
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to read input file",
                    file_path=str(input_path),
                    details=f"File reading error: {str(e)}",
                    original_exception=e,
                    context={'file_path': str(input_path)},
                    suggested_action="Check file accessibility and permissions"
                )
        
        # Content processing phases
        with error_boundary("header removal", LayerType.UTILITY):
            try:
                content = self.remove_header_or_logout(content)
                logger.debug("After header removal: %d chars", len(content))
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to remove headers from content",
                    data_type="markdown_content",
                    details=f"Header removal error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'operation': 'remove_header_or_logout'},
                    suggested_action="Check content format and header patterns"
                )
        
        with error_boundary("link cleaning", LayerType.UTILITY):
            try:
                content = self.remove_links_keep_text(content)
                logger.debug("After link cleaning: %d chars", len(content))
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to clean links from content",
                    data_type="markdown_content",
                    details=f"Link cleaning error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'operation': 'remove_links_keep_text'},
                    suggested_action="Check content format and link patterns"
                )
        
        with error_boundary("related companies removal", LayerType.UTILITY):
            try:
                content = self.remove_related_companies(content)
                logger.debug("After removing related companies: %d chars", len(content))
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to remove related companies from content",
                    data_type="markdown_content",
                    details=f"Related companies removal error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'operation': 'remove_related_companies'},
                    suggested_action="Check content format and company patterns"
                )
        
        with error_boundary("underscore tags removal", LayerType.UTILITY):
            try:
                content = self.remove_underscore_tags(content)
                logger.debug("After removing underscore tags: %d chars", len(content))
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to remove underscore tags from content",
                    data_type="markdown_content",
                    details=f"Underscore tags removal error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content), 'operation': 'remove_underscore_tags'},
                    suggested_action="Check content format and tag patterns"
                )
        
        # Output file handling
        with error_boundary("output file preparation", LayerType.UTILITY):
            output_path = output_file or input_file
            output_file_path = Path(output_path)
            
            # Ensure output directory exists
            output_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Validate output path
            if output_file_path.exists() and not output_file_path.is_file():
                raise FileProcessingException(
                    message="Output path exists but is not a file",
                    file_path=str(output_file_path),
                    details=f"Output path exists as directory: {output_file_path}",
                    context={'output_path': str(output_file_path)},
                    suggested_action="Choose a different output file path"
                )
        
        # File writing
        with error_boundary("file writing", LayerType.UTILITY):
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                logger.info("Saved cleaned file to: %s", output_path)
                return str(output_path)
                
            except PermissionError as e:
                raise FileProcessingException(
                    message="Permission denied writing file",
                    file_path=str(output_file_path),
                    details=f"File write access denied: {str(e)}",
                    original_exception=e,
                    context={'output_path': str(output_file_path)},
                    suggested_action="Check file permissions and directory access"
                )
            except Exception as e:
                raise FileProcessingException(
                    message="Failed to write output file",
                    file_path=str(output_file_path),
                    details=f"File writing error: {str(e)}",
                    original_exception=e,
                    context={'output_path': str(output_file_path), 'content_length': len(content)},
                    suggested_action="Check disk space and file permissions"
                )

    def clean_markdown_format(self, text: str) -> str:
        """Remove markdown formatting from text"""
        # Remove headers
        text = re.sub(r'^#+\s*', '', text)
        # Remove bold/italic
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        text = re.sub(r'\*(.*?)\*', r'\1', text)
        # Remove links
        text = re.sub(r'!?\[(.*?)\]\(.*?\)', r'\1', text)
        return text.strip()

    def _sanitize_title(self, title: str) -> str:
        """Sanitize a title for use in filenames"""
        if not title:
            return ""
        # Remove markdown table formatting
        title = re.sub(r'^\||\|$', '', title)
        # Remove special characters
        title = re.sub(r'[\\/*?:"<>|]', '', title)
        # Replace spaces with underscores
        title = re.sub(r'\s+', '_', title)
        # Limit length
        return title.strip()[:50]

    def _process_table_to_dataframe(self, current_table: List[str]) -> Optional['pd.DataFrame']:
        """Process a markdown table into a pandas DataFrame with consistent column handling"""
        
        if len(current_table) < 2:  # Need at least header and separator
            return None
            
        headers = [h.strip() for h in current_table[0].split('|')[1:-1]]
        rows = []
        max_cols = len(headers)
        
        # First pass: collect all rows and find max column count
        for row in current_table[2:]:
            if not row.strip().startswith('|'):
                continue
            cells = [c.strip() for c in row.split('|')[1:-1]]
            rows.append(cells)
            max_cols = max(max_cols, len(cells))
        
        # Pad headers if we found more columns than headers
        if max_cols > len(headers):
            logger.debug(f"Padding headers from {len(headers)} to {max_cols} columns")
            for i in range(len(headers), max_cols):
                headers.append(f"Column_{i+1}")
        
        # Ensure consistent column count for all rows
        if not rows:
            logger.debug("No data rows found in table")
            return None
            
        # Second pass: normalize all rows to max_cols
        for i, row in enumerate(rows):
            if len(row) > max_cols:
                logger.debug(f"Trimming row {i} from {len(row)} to {max_cols} columns")
                rows[i] = row[:max_cols]
            elif len(row) < max_cols:
                logger.debug(f"Padding row {i} from {len(row)} to {max_cols} columns")
                rows[i].extend([''] * (max_cols - len(row)))
        
        # Create dataframe with consistent column count
        return pd.DataFrame(rows, columns=headers)

    def _convert_md_content_to_excel(self, content: str, output_path: Path) -> None:
        """Convert markdown content to Excel file - shared implementation"""
        from openpyxl import Workbook # Lazy import
        from openpyxl.utils.dataframe import dataframe_to_rows # Lazy import
        
        # Create Excel workbook with single sheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Report"
        
        current_row = 1  # Track current row in Excel
        
        # Process each line while preserving structure
        in_table = False
        current_table = []
        
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Check if line is part of a table
            is_table_row = line.startswith('|') and line.endswith('|')
            
            if is_table_row:
                if not in_table:
                    # Start new table
                    in_table = True
                    current_table = []
                current_table.append(line)
            else:
                if in_table:
                    # Process completed table
                    df = self._process_table_to_dataframe(current_table)
                    if df is not None:
                        # Write table to Excel
                        for r in dataframe_to_rows(df, index=False, header=True):
                            for col_num, value in enumerate(r, 1):
                                ws.cell(row=current_row, column=col_num, value=value)
                            current_row += 1
                        current_row += 1  # Add blank row after table
                    
                    in_table = False
                    current_table = []
                
                # Clean and write non-table line to Excel
                clean_line = self.clean_markdown_format(line)
                if clean_line:  # Only write if line is not empty after cleaning
                    ws.cell(row=current_row, column=1, value=clean_line)
                    current_row += 1
        
        # Process any remaining table at end of file
        if in_table and current_table:
            df = self._process_table_to_dataframe(current_table)
            if df is not None:
                for r in dataframe_to_rows(df, index=False, header=True):
                    for col_num, value in enumerate(r, 1):
                        ws.cell(row=current_row, column=col_num, value=value)
                    current_row += 1
        
        # Save workbook
        wb.save(output_path)
        logger.info(f"Created structured Excel file: {output_path}")

    def convert_to_excel(self, markdown_file: str, output_file: Optional[str] = None) -> Optional[str]:
        """Convert markdown content to Excel file while preserving original structure.
        If markdown_file is a directory, converts all .md files in the directory."""
        
        input_path = Path(markdown_file)
        logger.debug(f"Converting {markdown_file} to {output_file}")
        logger.debug(f"Input path exists: {input_path.exists()}, is file: {input_path.is_file()}")
        
        # Handle directory input
        if input_path.is_dir():
            output_dir = Path(output_file) if output_file else input_path
            output_dir.mkdir(parents=True, exist_ok=True)
            
            for md_file in input_path.glob('*.md'):
                output_path = output_dir / f"{md_file.stem}.xlsx"
                self._convert_single_md_to_excel(md_file, output_path)
            return str(output_dir)
            
        # Handle single file input
        logger.debug(f"Reading markdown file: {markdown_file}")
        with open(markdown_file, 'r', encoding='utf-8') as f:
            content = f.read()
        logger.debug(f"Read {len(content)} characters from markdown file")
        
        output_path = output_file or Path(markdown_file).with_suffix('.xlsx')
        logger.debug(f"Saving Excel file to: {output_path}")
        
        try:
            self._convert_md_content_to_excel(content, Path(output_path))
            logger.info(f"Successfully created structured Excel file: {output_path}")
            return str(output_path)
        except Exception as e:
            logger.error(f"Failed to save Excel file: {str(e)}")
            raise

    def _convert_single_md_to_excel(self, md_file: Path, output_path: Path) -> None:
        """Helper method to convert a single markdown file to Excel"""
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Use the shared implementation
            self._convert_md_content_to_excel(content, output_path)
            
        except ImportError:
            logger.error("pandas or openpyxl not installed, cannot generate Excel")
            logger.error("Please install dependencies with:")
            logger.error("1. pip install pandas openpyxl")
            return None
        except Exception as e:
            logger.error(f"Excel conversion failed: {str(e)}")
            return None

    def command_convert_to_excel(self, input_file: str, output_file: Optional[str] = None, delete_original: bool = False) -> Optional[str]:
        """Execute convert to Excel command
        
        Args:
            input_file: Input markdown file or directory
            output_file: Output Excel file/directory path
            delete_original: Whether to delete original markdown file after conversion
        """
        result = self.convert_to_excel(input_file, output_file)
        if delete_original and result:
            input_path = Path(input_file)
            if input_path.is_file():
                os.remove(input_path)
                logger.info(f"Deleted original file: {input_path}")
            elif input_path.is_dir():
                for md_file in input_path.glob('*.md'):
                    os.remove(md_file)
                    logger.info(f"Deleted original file: {md_file}")
        return result

    def detect_data_blocks(self, df):
        """检测DataFrame中的数据区块"""
        from collections import deque
        import numpy as np
        
        rows, cols = df.shape
        visited = set()
        blocks = []
        
        for row in range(rows):
            for col in range(cols):
                # 检查单元格是否有值 (排除NaN/None/空字符串)
                cell_value = df.iat[row, col]
                has_value = not (pd.isna(cell_value) or cell_value == '')
                
                if (row, col) not in visited and has_value:
                    # BFS初始化
                    queue = deque([(row, col)])
                    visited.add((row, col))
                    min_row = max_row = row
                    min_col = max_col = col
                    
                    while queue:
                        r, c = queue.popleft()
                        # 8邻域检测
                        for dr in [-1, 0, 1]:
                            for dc in [-1, 0, 1]:
                                if dr == 0 and dc == 0:
                                    continue
                                nr, nc = r + dr, c + dc
                                if (0 <= nr < rows and 0 <= nc < cols and
                                    (nr, nc) not in visited):
                                    # 检查邻域单元格是否有值
                                    neighbor_value = df.iat[nr, nc]
                                    has_neighbor_value = not (pd.isna(neighbor_value) or neighbor_value == '')
                                    
                                    if has_neighbor_value:
                                        visited.add((nr, nc))
                                        queue.append((nr, nc))
                                        min_row, max_row = min(min_row, nr), max(max_row, nr)
                                        min_col, max_col = min(min_col, nc), max(max_col, nc)
                    
                    # 转换为1-based坐标并记录区块
                    blocks.append({
                        'left': min_col + 1,
                        'top': min_row + 1,
                        'width': max_col - min_col + 1,
                        'height': max_row - min_row + 1
                    })
        
        return blocks

    def _num_to_excel_col(self, n: int) -> str:
        """Convert 1-based column number to Excel column letter(s)"""
        letters = []
        while n > 0:
            n, rem = divmod(n - 1, 26)
            letters.append(chr(ord('A') + rem))
        return ''.join(reversed(letters))

    def command_split_xlsx(self, excel_file: str) -> List[str]:
        """Split Excel file into separate blocks with data block detection.
        Each block is saved as a separate XLSX file with pattern:
        original_filename_sheetname_blockcoordinates.xlsx
        where blockcoordinates is top-left cell like 'A1H1'"""
        try:
            import time
            
            logger.info(f"Splitting Excel file: {excel_file}")
            excel_path = Path(excel_file)
            output_files = []
            
            with pd.ExcelFile(excel_file) as excel_file:
                sheet_names = excel_file.sheet_names
                
                for sheet_name in sheet_names:
                    # 读取并清理数据
                    df = excel_file.parse(sheet_name)
                    df = df.map(lambda x: '' if isinstance(x, str) and 
                              x.strip().lower().startswith('unnamed: ') else x)
                    df.columns = ['' if str(col).strip().lower().startswith('unnamed: ') 
                                else col for col in df.columns]
                    
                    adjusted_df = df.copy()
                    adjusted_df.index = range(1, len(adjusted_df)+1)  # 1-based索引
                    # 在第一行插入列名作为数据
                    adjusted_df.loc[0] = df.columns
                    adjusted_df = adjusted_df.sort_index()
                    
                    # 检测数据区块
                    blocks = self.detect_data_blocks(adjusted_df)
                    logger.info(f"Sheet '{sheet_name}' data blocks detected: {len(blocks)}")
                    
                    for i, block in enumerate(blocks, 1):
                        logger.debug(f"  Block {i}: left={block['left']}, top={block['top']}, "
                                  f"width={block['width']}, height={block['height']}")
                        
                        # Extract block data
                        start_row = block['top'] - 1  # Convert to 0-based
                        end_row = start_row + block['height']
                        start_col = block['left'] - 1  # Convert to 0-based
                        end_col = start_col + block['width']
                        
                        block_df = adjusted_df.iloc[start_row:end_row, start_col:end_col]
                        
                        # Generate coordinate string (e.g. A1H1)
                        top_left_col = self._num_to_excel_col(block['left'])
                        top_left_row = block['top']
                        bottom_right_col = self._num_to_excel_col(block['left'] + block['width'] - 1)
                        bottom_right_row = block['top'] + block['height'] - 1
                        coord_str = f"{top_left_col}{top_left_row}{bottom_right_col}{bottom_right_row}"
                        
                        # Create new filename
                        new_filename = f"{excel_path.stem}_{sheet_name}_{coord_str}.xlsx"
                        new_path = excel_path.parent / new_filename
                        
                        # Save block to file
                        with pd.ExcelWriter(
                            new_path,
                            engine='openpyxl'
                        ) as writer:
                            block_df.columns = ['' for col in block_df.columns]
                            block_df.to_excel(writer, index=False)
                        
                        output_files.append(str(new_path))
            
            logger.info(f"Split Excel file into {len(output_files)} block files")
            return output_files
            
        except Exception as e:
            logger.error(f"Failed to split Excel file: {str(e)}")
            raise

    @log_and_reraise(logger, "markdown content cleaning")
    def _clean_markdown_content(self, content: str) -> str:
        """Clean and normalize markdown content by applying various regex substitutions"""
        set_operation_context("markdown_content_cleaning")
        
        with error_boundary("content cleaning validation", LayerType.UTILITY):
            if content is None:
                raise ValidationException(
                    message="Content cannot be None for cleaning",
                    field_name="content",
                    details="Content parameter is None",
                    context={'content_type': type(content).__name__},
                    suggested_action="Provide valid markdown content string"
                )
            
            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string for cleaning",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__, 'content_length': len(str(content)) if content else 0},
                    suggested_action="Provide a valid string content"
                )
        
        with error_boundary("content cleaning operations", LayerType.UTILITY):
            try:
                # Remove NaN and Unnamed columns
                cleaned_content = re.sub(r'NaN|Unnamed: \d+', r'', content)
                
                # Fix escaped characters
                cleaned_content = cleaned_content.replace('\\', '')
                
                # Convert **text** to ## text
                cleaned_content = re.sub(r'^\*\*(.+?)\*\*$', r'## \1', cleaned_content, flags=re.MULTILINE)
                
                logger.debug(f"Cleaned markdown content: original length={len(content)}, cleaned length={len(cleaned_content)}")
                return cleaned_content
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to apply regex cleaning patterns",
                    data_type="content_cleaning",
                    details=f"Regex pattern error during cleaning: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check regex pattern syntax and content format"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to clean markdown content",
                    data_type="content_cleaning",
                    details=f"Content cleaning error: {str(e)}",
                    original_exception=e,
                    context={'content_length': len(content)},
                    suggested_action="Check content format and cleaning operations"
                )

    @handle_layer_boundary(LayerType.UTILITY, "document processing")
    def process_document(self, doc_path: str, output_dir: str) -> List[str]:
        """Process a document (Excel or PDF) into markdown chunks"""
        set_operation_context("document_processing")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not doc_path:
                raise ValidationException(
                    message="Document path cannot be empty",
                    field_name="doc_path",
                    details="doc_path parameter is empty or None",
                    context={'provided_path': str(doc_path)},
                    suggested_action="Provide a valid document path"
                )
            
            if not output_dir:
                raise ValidationException(
                    message="Output directory cannot be empty",
                    field_name="output_dir",
                    details="output_dir parameter is empty or None",
                    context={'provided_output_dir': str(output_dir)},
                    suggested_action="Provide a valid output directory path"
                )
        
        doc = Path(doc_path)
        
        with error_boundary("file validation", LayerType.UTILITY):
            if not doc.exists():
                raise FileProcessingException(
                    message="Document file not found",
                    file_path=str(doc),
                    details=f"Document file does not exist: {doc}",
                    context={'document_path': str(doc), 'file_extension': doc.suffix.lower()},
                    suggested_action="Verify the document path and ensure the file exists"
                )
            
            if not doc.is_file():
                raise FileProcessingException(
                    message="Document path is not a file",
                    file_path=str(doc),
                    details=f"Document path points to a directory: {doc}",
                    context={'document_path': str(doc)},
                    suggested_action="Provide a path to a valid document file"
                )
        
        md_files = []
        
        if doc.suffix.lower() in ('.xls', '.xlsx'):
            md_files = self._process_excel_document(doc, output_dir)
        elif doc.suffix.lower() == '.pdf':
            md_files = self._process_pdf_document(doc, output_dir)
        else:
            raise ValidationException(
                message="Unsupported document format",
                field_name="doc_path",
                details=f"Document format not supported: {doc.suffix}",
                context={'document_path': str(doc), 'file_extension': doc.suffix.lower()},
                suggested_action="Provide an Excel (.xls, .xlsx) or PDF (.pdf) file"
            )
        
        return md_files

    @log_and_reraise(logger, "Excel document processing")
    def _process_excel_document(self, doc: Path, output_dir: str) -> List[str]:
        """Process Excel document into markdown chunks"""
        set_operation_context("excel_document_processing")
        
        with error_boundary("Excel processing dependencies", LayerType.UTILITY):
            try:
                from markitdown import MarkItDown
            except ImportError as e:
                raise ConfigurationException(
                    message="Excel processing dependencies not available",
                    details="markitdown package not installed",
                    original_exception=e,
                    config_key="markitdown",
                    context={'missing_package': 'markitdown'},
                    suggested_action="Install dependencies with: pip install markitdown"
                )
        
        with error_boundary("Excel file splitting", LayerType.UTILITY):
            try:
                logger.debug(f"Processing Excel file: {doc}")
                xls_files = self.command_split_xlsx(str(doc))
                logger.debug(f"Split Excel into {len(xls_files)} files")
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to split Excel file",
                    data_type="excel_splitting",
                    details=f"Excel splitting error: {str(e)}",
                    original_exception=e,
                    context={'document_path': str(doc)},
                    suggested_action="Check Excel file format and content"
                )
        
        with error_boundary("original file cleanup", LayerType.UTILITY):
            try:
                if doc.exists():
                    doc.unlink()
                    logger.debug(f"Deleted original Excel file: {doc}")
            except Exception as e:
                logger.warning(f"Failed to delete original Excel file: {str(e)}")
        
        with error_boundary("Excel to markdown conversion", LayerType.UTILITY):
            try:
                mid = MarkItDown(enable_plugins=False)
                md_files = []
                
                for xls_file in xls_files:
                    xls_path = Path(xls_file)
                    
                    with error_boundary("single Excel file conversion", LayerType.UTILITY):
                        try:
                            result = mid.convert(str(xls_path))
                            
                            if not result.text_content:
                                raise DataProcessingException(
                                    message="Excel conversion produced no content",
                                    data_type="excel_conversion",
                                    details="Converted content is empty",
                                    context={'excel_file': str(xls_path)},
                                    suggested_action="Check Excel file content and format"
                                )
                            
                            fixed_content = self._clean_markdown_content(result.text_content)
                            clean_path = Path(output_dir) / f"{xls_path.stem}.md"
                            
                            with open(clean_path, 'w', encoding='utf-8') as f:
                                f.write(fixed_content)
                            
                            chunks = self.split_by_tables(str(clean_path), output_dir, format_dates=True, format_numbers=True)
                            md_files.extend(chunks)
                            
                            # Cleanup temporary files
                            xls_path.unlink(missing_ok=True)
                            clean_path.unlink(missing_ok=True)
                            
                            logger.debug(f"Converted Excel to {len(chunks)} markdown chunks")
                            
                        except Exception as e:
                            if isinstance(e, (DataProcessingException, ConfigurationException)):
                                raise
                            raise DataProcessingException(
                                message="Failed to process individual Excel file",
                                data_type="excel_file_processing",
                                details=f"Individual Excel processing error: {str(e)}",
                                original_exception=e,
                                context={'excel_file': str(xls_path)},
                                suggested_action="Check individual Excel file format and content"
                            )
                
                return md_files
                
            except Exception as e:
                if isinstance(e, (DataProcessingException, ConfigurationException)):
                    raise
                raise DataProcessingException(
                    message="Failed to convert Excel files to markdown",
                    data_type="excel_conversion",
                    details=f"Excel conversion error: {str(e)}",
                    original_exception=e,
                    context={'excel_files_count': len(xls_files)},
                    suggested_action="Check Excel files and conversion dependencies"
                )

    @log_and_reraise(logger, "PDF document processing")
    def _process_pdf_document(self, doc: Path, output_dir: str) -> List[str]:
        """Process PDF document into markdown chunks with performance optimizations"""
        set_operation_context("pdf_document_processing")

        with error_boundary("PDF converter initialization", LayerType.UTILITY):
            # Use fast mode for financial documents to improve performance
            fast_mode = self.pdf_processing_mode == "fast"
            pdf_converter = self._get_pdf_converter(fast_mode=fast_mode)
            if not pdf_converter:
                raise ConfigurationException(
                    message="PDF converter not available",
                    details="Failed to initialize PDF converter",
                    context={'document_path': str(doc), 'processing_mode': self.pdf_processing_mode},
                    suggested_action="Check PDF processing dependencies"
                )
        
        with error_boundary("PDF processing dependencies", LayerType.UTILITY):
            try:
                from marker.output import text_from_rendered
            except ImportError as e:
                raise ConfigurationException(
                    message="PDF processing dependencies not available",
                    details="marker package not installed",
                    original_exception=e,
                    config_key="marker",
                    context={'missing_package': 'marker'},
                    suggested_action="Install dependencies with: pip install marker-pdf"
                )
        
        with error_boundary("PDF to markdown conversion", LayerType.UTILITY):
            try:
                logger.info(f"Converting PDF to markdown: {doc} (mode: {self.pdf_processing_mode})")

                # Performance monitoring
                conversion_start = time.time()
                rendered = pdf_converter(str(doc))
                conversion_time = time.time() - conversion_start

                text_extraction_start = time.time()
                text, _, _ = text_from_rendered(rendered)
                text_extraction_time = time.time() - text_extraction_start

                logger.info(f"PDF conversion completed in {conversion_time:.2f}s (extraction: {text_extraction_time:.2f}s)")
                
                if not text:
                    raise DataProcessingException(
                        message="PDF conversion produced no content",
                        data_type="pdf_conversion",
                        details="Converted content is empty",
                        context={'pdf_file': str(doc)},
                        suggested_action="Check PDF file content and format"
                    )
                
                fixed_content = self._clean_markdown_content(text)
                clean = self.remove_links_keep_text(fixed_content)
                clean_path = Path(output_dir) / f"{doc.stem}.md"
                
                with open(clean_path, 'w', encoding='utf-8') as f:
                    f.write(clean)
                
                # Cleanup original PDF
                doc.unlink(missing_ok=True)
                
                chunks = self.split_by_tables(str(clean_path), output_dir, format_dates=True, format_numbers=True)
                
                # Cleanup temporary markdown file
                clean_path.unlink(missing_ok=True)
                
                logger.debug(f"Converted PDF to {len(chunks)} markdown chunks")
                return chunks
                
            except Exception as e:
                if isinstance(e, (DataProcessingException, ConfigurationException)):
                    raise
                raise DataProcessingException(
                    message="Failed to convert PDF to markdown",
                    data_type="pdf_conversion",
                    details=f"PDF conversion error: {str(e)}",
                    original_exception=e,
                    context={'pdf_file': str(doc)},
                    suggested_action="Check PDF file format and conversion dependencies"
                )

    @handle_layer_boundary(LayerType.UTILITY, "document processing from files")
    def command_from_doc(self, input_files: List[str], output_dir: Optional[str] = None,
                        pdf_mode: str = "balanced") -> List[str]:
        """Process documents (Excel/PDF) into markdown chunks with unified error handling."""
        set_operation_context("document_processing_from_files")

        # Update PDF processing mode if specified
        if pdf_mode != "balanced":
            self.pdf_processing_mode = pdf_mode
            logger.info(f"Using PDF processing mode: {pdf_mode}")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not input_files:
                raise ValidationException(
                    message="Input files list cannot be empty",
                    field_name="input_files",
                    details="input_files parameter is empty or None",
                    context={'provided_files': input_files},
                    suggested_action="Provide a list of file paths or directories"
                )
            
            if not isinstance(input_files, list):
                raise ValidationException(
                    message="Input files must be a list",
                    field_name="input_files",
                    details=f"Expected list, got {type(input_files)}",
                    context={'input_type': type(input_files).__name__},
                    suggested_action="Provide a list of file paths or directories"
                )
        
        logger.info(f"Processing documents to markdown: {input_files}")
        all_md_files = []
        processed_files = set()
        
        with error_boundary("output directory resolution", LayerType.UTILITY):
            # Infer output directory from first input file if not specified
            if output_dir is None:
                first_file = next((f for f in input_files if not ('*' in f or '?' in f)), None)
                if first_file:
                    first_path = Path(first_file)
                    if first_path.is_file():
                        output_dir = str(first_path.parent)
                    else:
                        output_dir = str(first_path)
                else:
                    output_dir = str(Path.cwd())
            logger.info(f"Inferred output directory: {output_dir}")
        
        with error_boundary("directory creation", LayerType.UTILITY):
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
        
        for input_path in input_files:
            with error_boundary("input path processing", LayerType.UTILITY):
                try:
                    path = Path(input_path)
                    
                    with error_boundary("file discovery", LayerType.UTILITY):
                        if path.is_dir():
                            # Handle directory input - scan for supported files
                            logger.debug(f"Scanning directory: {path}")
                            files = []
                            files.extend(path.glob('*.xls'))
                            files.extend(path.glob('*.xlsx'))
                            files.extend(path.glob('*.pdf'))
                            logger.info(f"Found {len(files)} supported files in directory")
                        elif '*' in str(input_path) or '?' in str(input_path):
                            # Handle glob pattern
                            files = list(Path().glob(input_path))
                            logger.info(f"Found {len(files)} files matching pattern: {input_path}")
                        else:
                            # Handle single file
                            files = [path]
                    
                    with error_boundary("individual file processing", LayerType.UTILITY):
                        for file in files:
                            if str(file) in processed_files:
                                continue
                            processed_files.add(str(file))
                            
                            if file.suffix.lower() not in ('.xls', '.xlsx', '.pdf'):
                                logger.warning(f"Skipping unsupported file type: {file}")
                                continue
                                
                            try:
                                md_files = self.process_document(str(file), output_dir)
                                all_md_files.extend(md_files)
                            except Exception as e:
                                logger.error(f"Failed to process individual file {file}: {str(e)}")
                                continue
                                
                except Exception as e:
                    logger.error(f"Failed to process input path {input_path}: {str(e)}")
                    continue
                
        logger.info(f"Processed {len(processed_files)} documents into {len(all_md_files)} markdown chunks")
        return all_md_files

    @handle_layer_boundary(LayerType.UTILITY, "RAGFlow upload")
    def command_ragflow_upload(self, files: Optional[List[str]] = None,
                             directory: Optional[str] = None,
                             company_md: Optional[str] = None) -> dict:
        """
        Upload files to RAGFlow dataset with unified error handling.
        Requires either files or directory.
        Reads RAGFLOW_BASE_URL and RAGFLOW_API_KEY from environment.
        Optionally processes company markdown file.
        """
        set_operation_context("ragflow_upload")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not files and not directory:
                raise ValidationException(
                    message="Either files or directory must be provided",
                    field_name="files/directory",
                    details="Both files and directory parameters are None or empty",
                    context={'files_provided': bool(files), 'directory_provided': bool(directory)},
                    suggested_action="Provide either a list of files or a directory path"
                )
        
        with error_boundary("environment validation", LayerType.UTILITY):
            ragflow_base_url = os.getenv('RAGFLOW_BASE_URL')
            ragflow_api_key = os.getenv('RAGFLOW_API_KEY')
            
            if not ragflow_base_url:
                raise ConfigurationException(
                    message="RAGFLOW_BASE_URL environment variable not set",
                    details="RAGFLOW_BASE_URL is required for RAGFlow client initialization",
                    config_key="RAGFLOW_BASE_URL",
                    context={'environment_variables': list(os.environ.keys())},
                    suggested_action="Set RAGFLOW_BASE_URL environment variable"
                )
            
            if not ragflow_api_key:
                raise ConfigurationException(
                    message="RAGFLOW_API_KEY environment variable not set",
                    details="RAGFLOW_API_KEY is required for RAGFlow client authentication",
                    config_key="RAGFLOW_API_KEY",
                    context={'environment_variables': list(os.environ.keys())},
                    suggested_action="Set RAGFLOW_API_KEY environment variable"
                )
        
        with error_boundary("dependency validation", LayerType.UTILITY):
            try:
                from ragflow_api.client import RAGFlowClient
            except ImportError as e:
                raise ConfigurationException(
                    message="RAGFlow client not available",
                    details="ragflow_api package not installed",
                    original_exception=e,
                    config_key="ragflow_api",
                    context={'missing_package': 'ragflow_api'},
                    suggested_action="Install dependencies with: pip install ragflow-api"
                )
        
        logger.info("Starting RAGFlow upload process")
        start_time = time.time()
        
        with error_boundary("file collection", LayerType.UTILITY):
            upload_files = set()
            logger.info("Collecting files for upload")
            
            # Add provided files
            if files:
                with error_boundary("provided files processing", LayerType.UTILITY):
                    for file_path in files:
                        if file_path and os.path.exists(file_path):
                            file_path_obj = Path(file_path)
                            if file_path_obj.is_file():
                                upload_files.add(str(file_path_obj.resolve()))
                                logger.debug(f"Added file: {file_path}")
                            else:
                                logger.warning(f"Path is not a file: {file_path}")
                        else:
                            logger.warning(f"File not found or invalid: {file_path}")
            
            # Add files from directory
            if directory:
                with error_boundary("directory scanning", LayerType.UTILITY):
                    dir_path = Path(directory)
                    if dir_path.exists() and dir_path.is_dir():
                        logger.debug(f"Scanning directory: {directory}")
                        for ext in ['.xls', '.xlsx', '.md', '.pdf']:
                            files_found = list(dir_path.glob(f"*{ext}"))
                            upload_files.update([str(f.resolve()) for f in files_found])
                            logger.debug(f"Found {len(files_found)} {ext} files in directory")
                    else:
                        logger.warning(f"Directory not found or invalid: {directory}")
        
        logger.info(f"Collected {len(upload_files)} files for upload")
        
        with error_boundary("company markdown processing", LayerType.UTILITY):
            dataset_name = 'dataset'
            if company_md and os.path.exists(company_md):
                logger.info(f"Processing company markdown file: {company_md}")
                dataset_name = Path(company_md).stem
                logger.debug(f"Using company name for dataset: {dataset_name}")
                
                try:
                    logger.debug("Cleaning company markdown file")
                    self.command_clean_qcc(company_md)
                    
                    logger.debug("Converting company markdown to PDF")
                    pdf_path = self.convert_to_pdf(company_md)
                    if pdf_path and os.path.exists(pdf_path):
                        upload_files.add(str(Path(pdf_path).resolve()))
                        logger.info(f"Added company PDF to upload: {pdf_path}")
                    else:
                        logger.warning("Failed to convert company markdown to PDF")
                except Exception as e:
                    logger.error(f"Error processing company markdown: {str(e)}")
                    # Continue with upload even if company processing fails
        
        with error_boundary("dataset creation", LayerType.UTILITY):
            user_hash = hash(ragflow_api_key) % 10000
            dataset_name = f"{dataset_name}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_hash}"
            logger.info(f"Creating dataset with name: {dataset_name}")
            
            client = RAGFlowClient(
                base_url=ragflow_base_url,
                api_key=ragflow_api_key
            )
            logger.debug("RAGFlow client initialized")
            
            embedding_model = os.getenv('EMBEDDING_MODEL', 'BAAI/bge-m3')
            logger.info(f"Creating new dataset: {dataset_name} with {embedding_model}")
            
            create_start = time.time()
            create_response = client.create_dataset(
                name=dataset_name,
                embedding_model=embedding_model,
                chunk_method="one",
            )
            create_time = time.time() - create_start
            logger.info(f"Dataset created in {create_time:.2f}s")
            
            dataset_id = create_response.get('id')
            if not dataset_id:
                raise DataProcessingException(
                    message="Failed to get dataset ID from create response",
                    data_type="dataset_creation",
                    details="Create dataset response missing 'id' field",
                    context={'create_response': create_response},
                    suggested_action="Check RAGFlow API response format"
                )
            logger.debug(f"Dataset ID: {dataset_id}")
        
        with error_boundary("file upload", LayerType.UTILITY):
            document_ids = []
            if upload_files:
                logger.info(f"Uploading {len(upload_files)} files to dataset {dataset_id}")
                
                upload_start = time.time()
                upload_response = client.upload_documents(
                    dataset_id=dataset_id,
                    file_paths=list(upload_files)
                )
                upload_time = time.time() - upload_start
                logger.info(f"Upload completed in {upload_time:.2f}s")
                
                document_ids = [doc.get('id') for doc in upload_response if doc.get('id')]
                logger.info(f"Successfully uploaded {len(document_ids)} documents")
                
                # Update document metadata if JSON files exist
                self._update_documents_metadata(client, dataset_id, upload_response, list(upload_files))
        
        with error_boundary("document parsing", LayerType.UTILITY):
            if document_ids:
                batch_size = 10
                total_batches = (len(document_ids) + batch_size - 1) // batch_size
                logger.info(f"Starting document parsing in {total_batches} batches")
                
                for i in range(0, len(document_ids), batch_size):
                    batch = document_ids[i:i+batch_size]
                    batch_num = i//batch_size + 1
                    logger.debug(f"Parsing batch {batch_num}/{total_batches} with {len(batch)} documents")
                    
                    parse_start = time.time()
                    client.parse_documents(dataset_id=dataset_id, document_ids=batch)
                    
                    # Monitor parsing status
                    start_time = time.time()
                    pending_docs = set(batch)
                    logger.debug(f"Batch {batch_num}: Starting parsing status monitoring")
                    
                    while pending_docs:
                        time.sleep(5)
                        completed_docs = set()
                        
                        for doc_id in pending_docs:
                            try:
                                doc = client.list_documents(dataset_id=dataset_id, document_id=doc_id)
                                status = doc.get('docs', [{}])[0]
                                current_status = status.get('run')
                                
                                if current_status in ['DONE', 'CANCEL', 'FAIL']:
                                    completed_docs.add(doc_id)
                            except Exception as e:
                                logger.error(f"Error checking status for document {doc_id}: {str(e)}")
                                completed_docs.add(doc_id)
                        
                        pending_docs -= completed_docs
                        
                        if not pending_docs:
                            parse_time = time.time() - parse_start
                            logger.info(f"Batch {batch_num} parsing completed in {parse_time:.2f}s")
                            break
                            
                        if (time.time() - start_time) > 600:  # 10 min timeout
                            logger.warning(f"Batch {batch_num} parsing timed out after 10 minutes")
                            break
        
        total_time = time.time() - start_time
        logger.info(f"RAGFlow upload process completed in {total_time:.2f}s")
        
        return {
            'dataset_id': dataset_id,
            'document_ids': document_ids,
            'uploaded_files': list(upload_files)
        }

    @log_and_reraise(logger, "document metadata updates")
    def _update_documents_metadata(self, client, dataset_id: str, upload_response: List[dict], uploaded_files: List[str]) -> None:
        """
        Update document metadata from corresponding JSON files with unified error handling.
        """
        set_operation_context("document_metadata_updates")
        
        with error_boundary("input validation", LayerType.UTILITY):
            if not client:
                raise ValidationException(
                    message="RAGFlow client cannot be None",
                    field_name="client",
                    details="client parameter is None",
                    context={'client_provided': bool(client)},
                    suggested_action="Provide a valid RAGFlow client instance"
                )
            
            if not dataset_id:
                raise ValidationException(
                    message="Dataset ID cannot be empty",
                    field_name="dataset_id",
                    details="dataset_id parameter is empty or None",
                    context={'dataset_id_provided': bool(dataset_id)},
                    suggested_action="Provide a valid dataset ID"
                )
            
            if not upload_response:
                logger.warning("Upload response is empty, skipping metadata updates")
                return
            
            if not uploaded_files:
                logger.warning("Uploaded files list is empty, skipping metadata updates")
                return
        
        logger.info("Checking for metadata files to update document metadata")
        
        with error_boundary("file mapping creation", LayerType.UTILITY):
            # Create mapping of uploaded files to document IDs
            file_to_doc_map = {}
            for doc_info in upload_response:
                if 'id' in doc_info and 'name' in doc_info:
                    doc_id = doc_info['id']
                    doc_name = doc_info['name']
                    
                    # Find the corresponding uploaded file path
                    for file_path in uploaded_files:
                        if Path(file_path).name == doc_name:
                            file_to_doc_map[file_path] = doc_id
                            break
        
        logger.debug(f"Created file-to-document mapping for {len(file_to_doc_map)} files")
        
        with error_boundary("metadata file processing", LayerType.UTILITY):
            metadata_updates = 0
            
            for file_path, doc_id in file_to_doc_map.items():
                try:
                    # Skip non-Excel files for metadata processing
                    if not file_path.endswith('.xlsx'):
                        continue
                    
                    # Look for corresponding JSON metadata file
                    json_file_path = os.path.splitext(file_path)[0] + '.json'
                    
                    if not os.path.exists(json_file_path):
                        logger.debug(f"No metadata file found for {file_path}")
                        continue
                    
                    logger.debug(f"Found metadata file for {file_path}: {json_file_path}")
                    
                    # Read and parse JSON metadata
                    with open(json_file_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    if not metadata:
                        logger.warning(f"Empty metadata in file: {json_file_path}")
                        continue
                    
                    logger.debug(f"Parsed metadata for document {doc_id}")
                    
                    # Update document with metadata
                    update_response = client.update_document(
                        dataset_id=dataset_id,
                        document_id=doc_id,
                        meta_fields=metadata
                    )
                    
                    logger.info(f"Updated metadata for document {doc_id} (file: {Path(file_path).name})")
                    logger.debug(f"Update response: {update_response}")
                    metadata_updates += 1
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON metadata from {json_file_path}: {e}")
                except FileNotFoundError as e:
                    logger.error(f"Metadata file not found: {json_file_path}")
                except PermissionError as e:
                    logger.error(f"Permission denied reading metadata file: {json_file_path}")
                except Exception as e:
                    logger.error(f"Failed to update metadata for document {doc_id}: {e}")
        
        if metadata_updates > 0:
            logger.info(f"Successfully updated metadata for {metadata_updates} documents")
        else:
            logger.info("No metadata updates were performed")

    @log_and_reraise(logger, "PDF converter initialization")
    def _get_pdf_converter(self, fast_mode=False):
        """Safely initialize PDF converter instance with unified error handling and performance optimizations."""
        set_operation_context("pdf_converter_initialization")

        with error_boundary("PDF converter initialization", LayerType.UTILITY):
            if self._pdf_converter is not None:
                return self._pdf_converter

            try:
                from marker.converters.pdf import PdfConverter
                from marker.models import create_model_dict
            except ImportError as e:
                raise ConfigurationException(
                    message="PDF converter dependencies not available",
                    details="marker package not installed",
                    original_exception=e,
                    config_key="marker",
                    context={'missing_package': 'marker'},
                    suggested_action="Install dependencies with: pip install marker-pdf"
                )

            try:
                # Performance optimization: Create optimized model configuration
                if fast_mode:
                    # Fast mode: Reduce model complexity for speed
                    model_config = self._create_fast_model_dict()
                    logger.info("Initializing PDF converter in fast mode for improved performance")
                else:
                    model_config = create_model_dict()
                    logger.debug("Initializing PDF converter with default artifact dict")

                self._pdf_converter = PdfConverter(
                    artifact_dict=model_config
                )

            except Exception as e:
                raise ConfigurationException(
                    message="Failed to initialize PDF converter",
                    details="Error during PDF converter initialization",
                    original_exception=e,
                    config_key="pdf_converter",
                    context={'converter_type': 'PdfConverter', 'fast_mode': fast_mode},
                    suggested_action="Check marker package installation and model availability"
                )

            return self._pdf_converter

    def _create_fast_model_dict(self):
        """Create optimized model configuration for faster processing."""
        try:
            from marker.models import create_model_dict

            # Get default models but with performance optimizations
            models = create_model_dict()

            # Performance optimizations for financial documents
            # These settings prioritize speed over maximum accuracy
            if hasattr(models, 'layout'):
                # Reduce layout detection complexity
                models.layout.config.update({
                    'max_pages': 1,  # Process single pages faster
                    'batch_size': 1,  # Smaller batches for memory efficiency
                })

            if hasattr(models, 'table'):
                # Optimize table detection for financial tables
                models.table.config.update({
                    'confidence_threshold': 0.7,  # Lower threshold for faster detection
                    'max_tables_per_page': 5,  # Limit table detection scope
                })

            if hasattr(models, 'ocr'):
                # OCR optimizations
                models.ocr.config.update({
                    'batch_size': 4,  # Optimize batch processing
                    'confidence_threshold': 0.8,  # Balance speed vs accuracy
                })

            logger.debug("Created optimized model configuration for fast processing")
            return models

        except Exception as e:
            logger.warning(f"Failed to create fast model config, falling back to default: {e}")
            from marker.models import create_model_dict
            return create_model_dict()

    
