import json
import logging
import os
import time
import asyncio
from dotenv import load_dotenv
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException, Response
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi import status
from pathlib import Path
from typing import Optional
from pydantic import BaseModel
from contextlib import asynccontextmanager
from playwright.async_api import async_playwright

from .core import QccCrawler
from .session_manager import SessionManager
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Import unified error handling framework
from app.exceptions import BaseAppException
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from .exceptions import (
    <PERSON>rawlerServerException, LoginException, AccountRestrictedException,
    CrawlerSessionException, BrowserException, WebScrapingException,
    NetworkException, TimeoutException, CrawlerLayerType
)

# Import enhanced crawler logging
from .logging_config import (
    server_logger, set_crawler_session_context, set_crawler_browser_context,
    set_crawler_login_context, log_login_attempt, log_operation_timing
)

# Set up logging using shared configuration
setup_logging()
logger = server_logger

class BrowserManager:
    def __init__(self):
        self._browser = None
        self._playwright = None 
        self._context = None
        self._active_page = None
        self._lock = asyncio.Lock()
        self._session_manager = SessionManager()
        self._last_used = None
        self._max_tabs = 10  # 最大tab数量限制
        self._tab_count = 0  # 当前tab计数

    @log_and_reraise(logger, "browser instance management")
    async def get_browser(self):
        """Get or create browser instance with lock"""
        async with self._lock:
            with error_boundary("browser connection check", CrawlerLayerType.SERVER):
                if not self._browser or not self._browser.is_connected():
                    await self._cleanup()
                    await self._initialize()
                self._last_used = datetime.now()
                return self._browser

    @log_and_reraise(logger, "browser initialization")
    async def _initialize(self):
        """Initialize new browser instance and context"""
        with error_boundary("playwright startup", CrawlerLayerType.SERVER):
            try:
                self._playwright = await async_playwright().start()
            except Exception as e:
                raise BrowserException(
                    message="Failed to start Playwright",
                    browser_action="playwright_startup",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check Playwright installation and dependencies"
                )

        with error_boundary("browser launch", CrawlerLayerType.SERVER):
            try:
                headless = os.getenv("CRAWLER_HEADLESS", "true").lower() in ("true", "1", "t")
                self._browser = await self._playwright.chromium.launch(
                    headless=headless,
                    args=["--disable-blink-features=AutomationControlled"]
                )
            except Exception as e:
                raise BrowserException(
                    message="Failed to launch browser",
                    browser_action="browser_launch",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check system resources and browser installation"
                )

        with error_boundary("browser context creation", CrawlerLayerType.SERVER):
            try:
                # Create and store context
                self._context = await self._browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    viewport={"width": 1280, "height": 720}
                )
                await self._session_manager.load_session(self._context)
            except Exception as e:
                raise BrowserException(
                    message="Failed to create browser context",
                    browser_action="context_creation",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check browser permissions and session data"
                )

    async def get_active_page(self):
        """Get the shared active page, reusing the same tab"""
        async with self._lock:
            # 检查现有页面是否可用
            if self._active_page and not self._active_page.is_closed():
                logger.debug("Reusing existing active page")
                return self._active_page
                
            # 如果context不存在或已断开，重新初始化
            if not self._context or not self._context.browser.is_connected():
                await self._cleanup()
                await self._initialize()
                self._tab_count = 0  # 重置tab计数
            
            # 创建新页面
            self._active_page = await self._context.new_page()
            self._tab_count += 1
            logger.info(f"Created new tab, current tab count: {self._tab_count}")
            return self._active_page

    async def get_tab_count(self):
        """Get current tab count from browser context"""
        async with self._lock:
            if not self._context:
                return 0
            try:
                pages = self._context.pages
                actual_count = len(pages)
                # 同步内部计数器
                self._tab_count = actual_count
                return actual_count
            except Exception as e:
                logger.error(f"Failed to get tab count: {str(e)}", exc_info=True)
                return self._tab_count

    async def check_and_manage_tabs(self):
        """检查tab数量并在超过限制时进行管理"""
        if not self._context:
            return False
        try:
            pages = self._context.pages
            current_count = len(pages)
            # 同步内部计数器
            self._tab_count = current_count
            logger.info(f"Current tab count: {current_count}, max allowed: {self._max_tabs}")
            
            if current_count > self._max_tabs:
                logger.warning(f"Tab count ({current_count}) exceeds limit ({self._max_tabs}), need reset")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to check tab count: {str(e)}", exc_info=True)
            return False

    async def _cleanup(self):
        """Clean up existing browser instance"""
        try:
            if self._browser:
                try:
                    await self._browser.close()
                except:
                    pass
            if self._playwright:
                try:
                    await self._playwright.stop()
                except:
                    pass
        finally:
            self._browser = None
            self._playwright = None 
            self._context = None
            self._active_page = None
            self._lock = asyncio.Lock()
            self._last_used = None
            self._tab_count = 0  # 重置tab计数

    async def shutdown(self):
        """Clean shutdown of browser resources"""
        await self._cleanup()

    async def get_screenshot(self):
        """Get screenshot of current page"""
        async with self._lock:
            if not self._active_page or self._active_page.is_closed():
                self._active_page = await self.get_active_page()
            return await self._active_page.screenshot(full_page=True)

    async def get_page_content(self):
        """Get HTML content of current page"""
        async with self._lock:
            if not self._active_page or self._active_page.is_closed():
                self._active_page = await self.get_active_page()
            return await self._active_page.content()

class CrawlerManager:
    _instance = None
    _lock = asyncio.Lock()

    def __init__(self):
        self.crawler = None
        self.browser_manager = BrowserManager()

    @classmethod
    async def get_instance(cls):
        async with cls._lock:
            if cls._instance is None:
                cls._instance = CrawlerManager()
            return cls._instance

    async def get_crawler(self, page):
        async with self._lock:
            if (self.crawler is None or 
                not hasattr(self.crawler, 'context') or 
                not self.crawler.context or 
                not hasattr(self.crawler.context, 'browser') or 
                not self.crawler.context.browser.is_connected()):
                self.crawler = QccCrawler(page=page)
            return self.crawler

    async def reset(self, preserve_session=True):
        async with self._lock:
            # Save current session if needed
            if preserve_session and self.browser_manager._context:
                await self.browser_manager._session_manager.save_session(self.browser_manager._context)
            
            # Clean up old browser
            await self.browser_manager.shutdown()
            
            # Create new browser manager
            self.browser_manager = BrowserManager()
            
            # Reinitialize browser with saved session if available
            if preserve_session:
                await self.browser_manager._initialize()
                await self.browser_manager._session_manager.load_session(self.browser_manager._context)
            
            self.crawler = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.crawler_manager = await CrawlerManager.get_instance()
    yield
    # Shutdown
    await app.state.crawler_manager.browser_manager.shutdown()

app = FastAPI(title="QCC Crawler Server", version="1.0.0", lifespan=lifespan)
session_manager = SessionManager()

# Set layer context for server operations
set_layer_context("server")

# Global exception handlers for unified error handling
@app.exception_handler(BaseAppException)
async def handle_app_exception(request, exc: BaseAppException):
    """Handle all custom application exceptions"""
    exc.log_error(logger)

    # Convert to appropriate HTTP status code
    if isinstance(exc, LoginException):
        status_code = 401
    elif isinstance(exc, AccountRestrictedException):
        status_code = 403
    elif isinstance(exc, CrawlerSessionException):
        status_code = 400
    elif isinstance(exc, (BrowserException, WebScrapingException)):
        status_code = 500
    elif isinstance(exc, NetworkException):
        status_code = 502
    elif isinstance(exc, TimeoutException):
        status_code = 504
    else:
        status_code = 500

    return JSONResponse(
        status_code=status_code,
        content={
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details,
            "suggested_action": exc.suggested_action,
            "timestamp": exc.timestamp.isoformat()
        }
    )

@app.exception_handler(Exception)
async def handle_generic_exception(request, exc: Exception):
    """Handle any unhandled exceptions"""
    logger.error(f"Unhandled exception in crawler server: {str(exc)}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error_code": "CRAWLER_UNHANDLED_001",
            "message": "Internal server error",
            "details": "An unexpected error occurred",
            "suggested_action": "Please contact support if the problem persists"
        }
    )

class ProcessManager:
    LOCK_FILE = "crawl/session.lock"
    
    @classmethod
    def start(cls, pid: int, logged_in: bool = False):
        """Record process info to lock file"""
        try:
            os.makedirs("crawler", exist_ok=True)
            with open(cls.LOCK_FILE, "w") as f:
                json.dump({
                    "pid": pid,
                    "start_time": datetime.now().isoformat(),
                    "status": "running",
                    "logged_in": logged_in
                }, f)
            logger.info(f"Process {pid} started and recorded")
            return True
        except Exception as e:
            logger.error(f"Failed to record process: {str(e)}", exc_info=True)
            return False

    @classmethod
    def check(cls):
        """Check if process is still running"""
        if not os.path.exists(cls.LOCK_FILE):
            return False
            
        try:
            with open(cls.LOCK_FILE) as f:
                data = json.load(f)
            
            # Check if process exists
            os.kill(data["pid"], 0)
            
            # Check if exceeded max runtime (30 minutes)
            start_time = datetime.fromisoformat(data["start_time"])
            if (datetime.now() - start_time).total_seconds() > 1800:
                cls.stop()
                return False
                
            return True
        except (FileNotFoundError, json.JSONDecodeError):
            return False
        except OSError:  # Process doesn't exist
            return False
        except Exception as e:
            logger.error(f"Process check failed: {str(e)}", exc_info=True)
            return False

    @classmethod
    def stop(cls):
        """Stop process and clean up lock file"""
        try:
            if os.path.exists(cls.LOCK_FILE):
                os.remove(cls.LOCK_FILE)
            logger.info("Process stopped and lock file removed")
            return True
        except Exception as e:
            logger.error(f"Failed to stop process: {str(e)}", exc_info=True)
            return False

    @classmethod
    def update_login_status(cls, logged_in: bool):
        """Update login status"""
        if not os.path.exists(cls.LOCK_FILE):
            return False
            
        try:
            with open(cls.LOCK_FILE, 'r+') as f:
                data = json.load(f)
                data['logged_in'] = logged_in
                f.seek(0)
                json.dump(data, f)
                f.truncate()
            logger.info(f"Updated {cls.LOCK_FILE} login status to {logged_in}")
            return True
        except Exception as e:
            logger.error(f"Failed to update login status: {str(e)}", exc_info=True)
            return False

    @classmethod
    def get_info(cls):
        try:
            return session_manager.read_status()
        except Exception as e:
            logger.error(f"Failed to get process info: {str(e)}", exc_info=True)
            return None

@app.get("/screenshot")
@handle_layer_boundary(CrawlerLayerType.SERVER, "capture browser screenshot")
async def get_screenshot():
    """Get screenshot of current browser page"""
    set_operation_context("capture_screenshot")
    logger.info("Starting screenshot capture")
    
    with error_boundary("screenshot capture", CrawlerLayerType.SERVER):
        screenshot = await app.state.crawler_manager.browser_manager.get_screenshot()
        return Response(content=screenshot, media_type="image/png")

@app.get("/page_content")
@handle_layer_boundary(CrawlerLayerType.SERVER, "get browser page content")
async def get_page_content():
    """Get HTML content of current browser page"""
    set_operation_context("get_page_content")
    logger.info("Starting page content retrieval")
    
    with error_boundary("page content retrieval", CrawlerLayerType.SERVER):
        content = await app.state.crawler_manager.browser_manager.get_page_content()
        return Response(content=content, media_type="text/html")

@app.get("/status")
@handle_layer_boundary(CrawlerLayerType.SERVER, "check login status for specified URL")
async def get_status(url: str = "https://www.qcc.com"):
    """Get current crawler status by checking actual login state for specified URL"""
    set_operation_context("check_login_status")
    logger.info(f"Starting login status check for URL: {url}")
    
    with error_boundary("login status check", CrawlerLayerType.SERVER):
        logger.info(f"Checking actual login status for URL: {url}")
        
        # Get active page to check login status
        with error_boundary("browser page retrieval", CrawlerLayerType.SERVER):
            try:
                page = await app.state.crawler_manager.browser_manager.get_active_page()
                crawler = await app.state.crawler_manager.get_crawler(page)
            except Exception as e:
                raise BrowserException(
                    message="Failed to get browser page for status check",
                    browser_action="page_retrieval",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check browser state and try resetting"
                )
        
        # Actually check login status by visiting the specified URL
        with error_boundary("login verification", CrawlerLayerType.SERVER):
            try:
                login_status = await crawler.check_login(url, page)
            except Exception as e:
                raise WebScrapingException(
                    message="Failed to verify login status",
                    operation="login_verification",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check network connectivity and URL accessibility"
                )
        
        logger.debug(f"Real-time login status for {url}: {login_status}")
        
        # Return actual login status
        return JSONResponse(
            content={
                "logged_in": login_status.get('login', False),
                "status": "active" if login_status.get('login', False) else "not_logged_in",
                "message": "已登录" if login_status.get('login', False) else "未登录",
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "screen": login_status.get('screen', '') if not login_status.get('login', False) else None
            },
            status_code=200
        )

@app.get("/tabs")
@handle_layer_boundary(CrawlerLayerType.SERVER, "get browser tab information")
async def get_tab_info():
    """Get current browser tab information"""
    set_operation_context("get_tab_info")
    logger.info("Starting tab information retrieval")
    
    with error_boundary("browser tab retrieval", CrawlerLayerType.SERVER):
        try:
            tab_info = await app.state.crawler_manager.browser_manager.get_tab_info()
            logger.debug(f"Retrieved tab information: {len(tab_info)} tabs")
            return JSONResponse(content=tab_info, status_code=200)
        except Exception as e:
            raise BrowserException(
                message="Failed to retrieve browser tab information",
                browser_action="tab_info_retrieval",
                details=str(e),
                original_exception=e,
                suggested_action="Check browser state and connection"
            )

@app.post("/login")
@handle_layer_boundary(CrawlerLayerType.SERVER, "user login process")
async def login():
    """Login to QCC"""
    set_operation_context("user_login")
    logger.info("Starting login process")
    ProcessManager.start(os.getpid())
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        # Get browser page and crawler instance
        with error_boundary("browser page setup", CrawlerLayerType.SERVER):
            logger.info("Getting shared active page...")
            page = await app.state.crawler_manager.browser_manager.get_active_page()

            logger.info("Getting QccCrawler instance with shared browser context...")
            crawler = await app.state.crawler_manager.get_crawler(page)

        # Check current login status
        with error_boundary("login status check", CrawlerLayerType.SERVER):
            login_status = await crawler.check_login("https://www.qcc.com", page)

            if login_status.get('login', False):
                logger.info("Already logged in - updating status")
                session_manager.save_status({
                    "logged_in": True,
                    "timestamp": datetime.now().isoformat(),
                    "screen": None
                })
                ProcessManager.update_login_status(True)
                return JSONResponse(
                    content={
                        "logged_in": True,
                        "status": "already_logged_in",
                        "message": "User already logged in"
                    },
                    status_code=200
                )

            # Check for account restrictions
            if login_status.get("status") == "permanent_failed" or login_status.get("status") == "rate_limited":
                restriction_type = "permanent" if login_status.get("status") == "permanent_failed" else "rate_limited"

                session_manager.save_status({
                    "logged_in": False,
                    "timestamp": datetime.now().isoformat(),
                    "screen": login_status.get("screen", ''),
                    "error": "Account permanently restricted"
                })

                raise AccountRestrictedException(
                    message="Account access restricted",
                    restriction_type=restriction_type,
                    details=f"Login status: {login_status.get('status')}",
                    context={
                        'screen': login_status.get("screen", ''),
                        'login_status': login_status
                    }
                )

            await asyncio.sleep(2)
            
        # Perform login attempts
        with error_boundary("login attempt process", CrawlerLayerType.SERVER):
            max_attempts = 3
            for attempt in range(max_attempts):
                logger.info(f"Login attempt {attempt + 1}/{max_attempts} (3s timeout)")

                try:
                    login_result = await crawler.perform_login("https://www.qcc.com", page, timeout=3)
                    ls = {
                        "logged_in": login_result.get("login"),
                        "screen": login_result.get("screen", '')[:20],
                        "status": login_result.get("status")
                    }
                    logger.info(f"Login result: {ls}")

                    if not login_result:
                        raise LoginException(
                            message="Login returned empty result",
                            login_status="empty_result",
                            details="Login function returned None or empty result"
                        )

                    # Check for account restrictions again
                    if login_result.get("status") == "permanent_failed" or login_result.get("status") == "rate_limited":
                        restriction_type = "permanent" if login_result.get("status") == "permanent_failed" else "rate_limited"

                        session_manager.save_status({
                            "logged_in": False,
                            "timestamp": datetime.now().isoformat(),
                            "screen": None,
                            "error": "Account permanently restricted"
                        })

                        raise AccountRestrictedException(
                            message="Account access restricted during login",
                            restriction_type=restriction_type,
                            details=f"Login attempt failed with status: {login_result.get('status')}",
                            context={
                                'screen': login_result.get("screen", ''),
                                'login_result': login_result
                            }
                        )
                        
                    # Handle QR code requirement
                    screen = login_result.get("screen")
                    if screen:
                        logger.info("Detected QR code login requirement")
                        return JSONResponse(
                            content={
                                "logged_in": False,
                                "status": "qr_code_required",
                                "screen": screen
                            },
                            status_code=200
                        )

                    # Handle successful login
                    if login_result.get("login"):
                        logger.info("Detected logged in status - ending login process")
                        session_manager.save_status({
                            "logged_in": True,
                            "timestamp": datetime.now().isoformat(),
                            "screen": None
                        })
                        ProcessManager.update_login_status(True)
                        return JSONResponse(
                            content={
                                "logged_in": True,
                                "status": "logged_in",
                                "message": "Login successful"
                            },
                            status_code=200
                        )

                except Exception as e:
                    logger.warning(f"Login attempt {attempt + 1} failed: {str(e)}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(3)  # Wait before retry
                    else:
                        raise LoginException(
                            message="All login attempts failed",
                            login_status="attempts_exhausted",
                            details=f"Failed after {max_attempts} attempts: {str(e)}",
                            original_exception=e,
                            suggested_action="Check network connectivity and try again later"
                        )

        # If we get here, all attempts failed
        raise LoginException(
            message="Login attempts exhausted",
            login_status="failed",
            details=f"All {max_attempts} login attempts failed",
            suggested_action="Check credentials and network connectivity"
        )

    # This should never be reached due to the raise above, but just in case
    raise LoginException(
        message="Login process completed without success",
        login_status="unexpected_completion",
        details="Login loop completed without returning a result"
    )

class CrawlerRequest(BaseModel):
    home_url: str = "www.qcc.com"
    keyword: str

@app.post("/reset")
@handle_layer_boundary(CrawlerLayerType.SERVER, "reset browser and crawler instances")
async def reset_browser():
    """Force reset browser and crawler instances"""
    set_operation_context("reset_browser")
    logger.info("Starting browser reset process")
    
    with error_boundary("browser reset", CrawlerLayerType.SERVER):
        logger.info("Resetting browser while preserving session state")
        await app.state.crawler_manager.reset(preserve_session=True)
        return JSONResponse(
            content={
                "status": "reset",
                "message": "Browser and crawler instances reset successfully"
            },
            status_code=200
        )

@app.post("/start")
@handle_layer_boundary(CrawlerLayerType.SERVER, "start crawler with parameters")
async def start_crawler(request: CrawlerRequest):
    """Start crawler with given parameters"""
    set_operation_context("start_crawler")
    logger.info(f"Starting crawler process, URL: {request.home_url}, Keyword: {request.keyword}")
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            with error_boundary("crawler initialization", CrawlerLayerType.SERVER):
                logger.info(f"Starting crawler, URL: {request.home_url}, Keyword: {request.keyword}")
                
                logger.info("Getting shared active page...")
                try:
                    page = await app.state.crawler_manager.browser_manager.get_active_page()
                    
                    # Verify browser/context is still active
                    if not page or page.is_closed() or not page.context.browser.is_connected():
                        logger.warning("Browser/context closed during crawl - reinitializing")
                        await app.state.crawler_manager.reset()
                        page = await app.state.crawler_manager.browser_manager.get_active_page()
                        
                    logger.info("Getting QccCrawler singleton instance...")
                    crawler = await app.state.crawler_manager.get_crawler(page)
                except Exception as e:
                    raise BrowserException(
                        message="Failed to initialize crawler browser components",
                        browser_action="crawler_initialization",
                        details=str(e),
                        original_exception=e,
                        suggested_action="Check browser state and try resetting"
                    )
                
            # First check login status
            with error_boundary("login verification", CrawlerLayerType.SERVER):
                try:
                    login_status = await crawler.check_login(request.home_url, page)
                    if not login_status.get('login', False):
                        logger.warning("Login required before crawling")
                        raise LoginException(
                            message="User not logged in",
                            login_status="not_authenticated",
                            details="Login verification failed before crawling",
                            suggested_action="Please login before starting crawler"
                        )
                except LoginException:
                    raise  # Re-raise LoginException as-is
                except Exception as e:
                    raise WebScrapingException(
                        message="Failed to verify login status before crawling",
                        operation="login_verification",
                        details=str(e),
                        original_exception=e,
                        suggested_action="Check network connectivity and login state"
                    )
            
            # If logged in, perform read operation
            with error_boundary("crawler execution", CrawlerLayerType.SERVER):
                try:
                    result = await crawler.read(request.home_url, request.keyword, page)
                    
                    # Check tab count and auto-reset if needed
                    need_reset = await app.state.crawler_manager.browser_manager.check_and_manage_tabs()
                    if need_reset:
                        logger.info("Tab count exceeded limit, performing automatic reset after crawling")
                        try:
                            await app.state.crawler_manager.reset(preserve_session=True)
                            logger.info("Automatic reset completed successfully")
                            result["auto_reset"] = True
                            result["reset_reason"] = "Tab count exceeded limit"
                        except Exception as reset_error:
                            logger.error(f"Automatic reset failed: {str(reset_error)}", exc_info=True)
                            result["auto_reset_failed"] = True
                            result["reset_error"] = str(reset_error)
                    
                    logger.info("Crawling completed successfully")
                    return JSONResponse(
                        content={
                            "status": "completed",
                            "companies": result.get("company", []),
                            "page_content": result.get("page_content", ""),
                            "code": result.get("code", 500),
                            "raw_data": result,
                            "auto_reset": result.get("auto_reset", False),
                            "reset_reason": result.get("reset_reason", ""),
                            "auto_reset_failed": result.get("auto_reset_failed", False),
                            "reset_error": result.get("reset_error", "")
                        },
                        status_code=result.get("code", 500)
                    )
                except Exception as e:
                    raise WebScrapingException(
                        message="Crawler execution failed",
                        operation="data_extraction",
                        details=str(e),
                        original_exception=e,
                        suggested_action="Check target website accessibility and crawler configuration"
                    )

        except LoginException as e:
            logger.error(f"Login verification failed: {e.message}", exc_info=True)
            return JSONResponse(
                content={
                    "status": "login_required",
                    "message": "User not logged in",
                    "code": 401
                },
                status_code=401
            )
        except (BrowserException, WebScrapingException) as e:
            logger.error(f"Specific crawler error (attempt {retry_count + 1}): {e.message}", exc_info=True)
            retry_count += 1
            if retry_count >= max_retries:
                raise e  # Let global handler process it
            await asyncio.sleep(2)  # Brief delay before retry
        except Exception as e:
            logger.error(f"Unexpected crawler error (attempt {retry_count + 1}): {str(e)}", exc_info=True)
            retry_count += 1
            if retry_count >= max_retries:
                raise CrawlerServerException(
                    message="Crawler failed after maximum retries",
                    operation="crawler_execution",
                    details=f"Failed after {max_retries} attempts: {str(e)}",
                    original_exception=e,
                    suggested_action="Check network connectivity, browser state, and try again"
                )
            await asyncio.sleep(2)  # Brief delay before retry

if __name__ == "__main__":
    import uvicorn
    load_dotenv(override=True)
    host = os.getenv("SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("CRAWLER_SERVER_PORT", "8000"))
    uvicorn.run(app, host=host, port=port)
