"""
PDF Processing Performance Configuration

This module provides configuration options for optimizing PDF processing performance
based on different use cases and requirements.
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

@dataclass
class PDFProcessingConfig:
    """Configuration for PDF processing performance optimization."""
    
    # Processing mode: fast, balanced, accurate
    mode: str = "balanced"
    
    # Model configuration
    enable_layout_detection: bool = True
    enable_table_detection: bool = True
    enable_ocr: bool = True
    
    # Performance tuning
    batch_size: int = 1
    max_pages_per_batch: int = 1
    confidence_threshold: float = 0.8
    
    # Memory optimization
    enable_model_caching: bool = True
    max_memory_usage_mb: int = 4096
    
    # Timeout settings
    processing_timeout_seconds: int = 300
    model_load_timeout_seconds: int = 120

class PDFPerformanceOptimizer:
    """Manages PDF processing performance optimizations."""
    
    PRESET_CONFIGS = {
        "fast": PDFProcessingConfig(
            mode="fast",
            batch_size=1,
            confidence_threshold=0.7,
            max_pages_per_batch=1,
            processing_timeout_seconds=180,
            model_load_timeout_seconds=60
        ),
        "balanced": PDFProcessingConfig(
            mode="balanced",
            batch_size=2,
            confidence_threshold=0.8,
            max_pages_per_batch=2,
            processing_timeout_seconds=300,
            model_load_timeout_seconds=90
        ),
        "accurate": PDFProcessingConfig(
            mode="accurate",
            batch_size=4,
            confidence_threshold=0.9,
            max_pages_per_batch=4,
            processing_timeout_seconds=600,
            model_load_timeout_seconds=180
        )
    }
    
    @classmethod
    def get_config(cls, mode: str = "balanced") -> PDFProcessingConfig:
        """Get configuration for specified processing mode."""
        if mode not in cls.PRESET_CONFIGS:
            logger.warning(f"Unknown mode '{mode}', falling back to 'balanced'")
            mode = "balanced"
        
        config = cls.PRESET_CONFIGS[mode]
        logger.info(f"Using PDF processing configuration: {mode}")
        return config
    
    @classmethod
    def get_marker_config(cls, mode: str = "balanced") -> Dict[str, Any]:
        """Get marker library specific configuration."""
        config = cls.get_config(mode)
        
        marker_config = {
            "batch_size": config.batch_size,
            "max_pages": config.max_pages_per_batch,
            "confidence_threshold": config.confidence_threshold,
        }
        
        # Mode-specific optimizations
        if mode == "fast":
            marker_config.update({
                "force_ocr": False,
                "extract_images": False,
                "detect_handwriting": False,
                "extract_tables": True,  # Keep table extraction for financial docs
                "extract_forms": False,
            })
        elif mode == "balanced":
            marker_config.update({
                "force_ocr": False,
                "extract_images": False,
                "detect_handwriting": False,
                "extract_tables": True,
                "extract_forms": True,
            })
        else:  # accurate
            marker_config.update({
                "force_ocr": True,
                "extract_images": True,
                "detect_handwriting": True,
                "extract_tables": True,
                "extract_forms": True,
            })
        
        return marker_config
    
    @classmethod
    def optimize_for_financial_documents(cls, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply optimizations specific to financial documents."""
        optimized = base_config.copy()
        
        # Financial documents typically have:
        # - Structured tables (high priority)
        # - Minimal images (can disable)
        # - Standard fonts (OCR not always needed)
        # - Forms and structured data
        
        optimized.update({
            "prioritize_tables": True,
            "table_confidence_threshold": 0.7,  # Lower threshold for financial tables
            "extract_images": False,  # Financial docs rarely need image extraction
            "detect_charts": True,  # But may have charts/graphs
            "preserve_formatting": True,  # Important for financial data
        })
        
        return optimized

def get_environment_config() -> PDFProcessingConfig:
    """Get configuration from environment variables."""
    mode = os.getenv("PDF_PROCESSING_MODE", "balanced")
    
    config = PDFPerformanceOptimizer.get_config(mode)
    
    # Override with environment variables if present
    if os.getenv("PDF_BATCH_SIZE"):
        config.batch_size = int(os.getenv("PDF_BATCH_SIZE"))
    
    if os.getenv("PDF_CONFIDENCE_THRESHOLD"):
        config.confidence_threshold = float(os.getenv("PDF_CONFIDENCE_THRESHOLD"))
    
    if os.getenv("PDF_PROCESSING_TIMEOUT"):
        config.processing_timeout_seconds = int(os.getenv("PDF_PROCESSING_TIMEOUT"))
    
    return config

# Performance monitoring utilities
class PerformanceMonitor:
    """Monitor and log PDF processing performance."""
    
    def __init__(self):
        self.metrics = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation."""
        import time
        self.metrics[operation] = {"start": time.time()}
    
    def end_timer(self, operation: str):
        """End timing an operation and log results."""
        import time
        if operation in self.metrics:
            elapsed = time.time() - self.metrics[operation]["start"]
            self.metrics[operation]["duration"] = elapsed
            logger.info(f"Performance: {operation} completed in {elapsed:.2f}s")
            return elapsed
        return 0
    
    def get_summary(self) -> Dict[str, float]:
        """Get performance summary."""
        return {op: data.get("duration", 0) for op, data in self.metrics.items()}
