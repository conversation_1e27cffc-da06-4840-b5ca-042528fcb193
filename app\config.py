from dataclasses import dataclass
import os
from dotenv import load_dotenv

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
import logging

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for configuration operations
set_layer_context("configuration")

load_dotenv(override=True)


@dataclass
class AgentConfig:
    """Agent运行时配置参数"""
    workspace_id: str
    api_key: str = os.getenv("OPENAI_API_KEY", "")
    api_base: str = os.getenv("OPENAI_API_BASE", "")
    log_level: str = "INFO"
    log_file: str = "default.log"
    log_max_bytes: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 2  # Keep 2 log files
    model_name: str = os.getenv("MODEL_ID", "QwQ-32b")
    temperature: float = 0.2
    max_templates: int = 3
    llm_backend: str = os.getenv("LLM_BACKEND", "OLLAMA")

    @log_and_reraise(logger, "agent configuration validation")
    def validate(self):
        """验证必要配置参数 with unified error handling"""
        set_operation_context("agent_config_validation")

        with error_boundary("configuration validation", LayerType.CONFIGURATION):
            if not self.llm_backend:
                raise ConfigurationException(
                    message="LLM backend is required",
                    details="LLM_BACKEND environment variable is not set or empty",
                    context={'llm_backend': self.llm_backend},
                    suggested_action="Set LLM_BACKEND environment variable (e.g., 'OLLAMA', 'OPENAI')"
                )

            if not self.workspace_id:
                raise ConfigurationException(
                    message="Workspace ID is required",
                    details="workspace_id parameter is not set or empty",
                    context={'workspace_id': self.workspace_id},
                    suggested_action="Provide a valid workspace_id when creating AgentConfig"
                )

            if not self.model_name:
                raise ConfigurationException(
                    message="Model name is required",
                    details="MODEL_ID environment variable is not set or empty",
                    context={'model_name': self.model_name},
                    suggested_action="Set MODEL_ID environment variable with a valid model name"
                )

    @classmethod
    @log_and_reraise(logger, "agent configuration loading from file")
    def from_file(cls, config_file: str):
        """Load configuration from file with unified error handling"""
        set_operation_context("agent_config_from_file")

        with error_boundary("configuration file loading", LayerType.CONFIGURATION):
            try:
                import json
                from pathlib import Path

                config_path = Path(config_file)
                if not config_path.exists():
                    raise ConfigurationException(
                        message="Configuration file not found",
                        details=f"Configuration file does not exist: {config_file}",
                        context={'config_file': config_file},
                        suggested_action="Check the configuration file path and ensure it exists"
                    )

                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # Create instance with loaded data
                instance = cls(**config_data)
                instance.validate()
                return instance

            except json.JSONDecodeError as e:
                raise ConfigurationException(
                    message="Invalid JSON in configuration file",
                    details=f"JSON parsing error: {str(e)}",
                    original_exception=e,
                    context={'config_file': config_file},
                    suggested_action="Check JSON syntax in configuration file"
                )
            except (IOError, OSError) as e:
                raise ConfigurationException(
                    message="Failed to read configuration file",
                    details=f"File reading error: {str(e)}",
                    original_exception=e,
                    context={'config_file': config_file},
                    suggested_action="Check file permissions and accessibility"
                )
            except Exception as e:
                if isinstance(e, ConfigurationException):
                    raise
                raise ConfigurationException(
                    message="Failed to load configuration from file",
                    details=f"Configuration loading error: {str(e)}",
                    original_exception=e,
                    context={'config_file': config_file},
                    suggested_action="Check configuration file format and content"
                )
