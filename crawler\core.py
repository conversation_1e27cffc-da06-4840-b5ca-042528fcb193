import asyncio
import json
import logging
from logging.handlers import RotatingFileHandler
import os
import base64
import random
from datetime import datetime
from typing import List, Dict
import playwright
from playwright.async_api import async_playwright
from playwright._impl._errors import TargetClosedError
from pathlib import Path
from .utilities import sanitize_filename, ensure_output_dir
from .session_manager import SessionManager
import html2text
import re

# Import unified error handling framework
from app.exceptions import LayerType, ErrorSeverity
from app.error_handling import log_and_reraise, error_boundary
from app.logging_config import set_layer_context, set_operation_context
from .exceptions import (
    WebScrapingException, BrowserException, PageLoadException,
    ElementNotFoundException, DataExtractionException, SearchException,
    RateLimitException, CaptchaException, NetworkException, TimeoutException,
    LoginException, AccountRestrictedException, CrawlerLayerType
)

# Import enhanced crawler logging
from .logging_config import (
    core_logger, set_crawler_page_context, set_crawler_keyword_context,
    set_crawler_login_context, log_browser_action, log_search_operation,
    log_login_attempt, log_operation_timing
)

logger = core_logger

class QccCrawler:
    def __init__(self, output_dir: str = "./results", keyword: str = None,
                 session_file_path: str = "session.json", page=None, browser_context=None):
        """Initialize QCC Crawler with unified error handling"""
        # Set layer context for logging
        set_layer_context("core")

        try:
            self.output_dir = output_dir
            self.keyword = keyword
            self.cookies = None
            self.session_file_path = session_file_path
            self.context = browser_context
            self.session = SessionManager(session_file_path)

            # Ensure output directory exists
            with error_boundary("output directory creation", CrawlerLayerType.CORE):
                ensure_output_dir(self.output_dir)

        except Exception as e:
            raise BrowserException(
                message="Failed to initialize crawler",
                details=f"Initialization error: {str(e)}",
                browser_action="initialization",
                context={
                    'output_dir': output_dir,
                    'session_file_path': session_file_path
                },
                original_exception=e,
                suggested_action="Check file permissions and directory paths"
            )

    async def _process_search_results(self, items: List[Dict]):
        logger.debug("开始处理搜索结果")
        ensure_output_dir(self.output_dir)
        logger.debug("输出目录已确保存在: %s", self.output_dir)
        matched = False
        all_names = []
        logger.debug("共收到%d条搜索结果", len(items))

        for item in items:
            if not all(key in item for key in ("KeyNo", "name")):
                continue

            all_names.append(item['name'])

            # 精确匹配关键词
            if self.keyword and item['name'].strip() == self.keyword.strip():
                logger.debug("找到匹配项: %s", item['name'])
                url = f"https://www.qcc.com/firm/{item['KeyNo']}.html"
                logger.debug("目标URL: %s", url)
                filename = sanitize_filename(item["name"]) + ".md"
                output_path = Path(self.output_dir) / filename
                logger.debug("输出文件路径: %s", output_path)
                # async with AsyncWebCrawler(cookies=await self.context.cookies()) as crawler:
                #     result = await crawler.arun(url=url)
                #     if result and result.markdown:
                #         output_path.write_text(result.markdown, encoding="utf-8")
                #         matched = True

        if self.keyword and not matched:
            raise Exception(f"未找到完全匹配'{self.keyword}'的企业，可用名称：{', '.join(all_names)}")

    async def _process_target_results(self, page, name):
        logger.debug("开始处理目标结果，名称: %s", name)
        ensure_output_dir(self.output_dir)
        logger.debug("输出目录已确保存在: %s", self.output_dir)
        filename = sanitize_filename(name) + ".md"
        output_path = Path(self.output_dir) / filename
        logger.debug("输出文件路径: %s", output_path)

        # 获取页面html content
        logger.debug("正在获取页面HTML内容...")
        html_content = await page.content()
        logger.debug("获取到HTML内容，长度: %d字符", len(html_content))

        # 配置html转换器
        converter = html2text.HTML2Text()
        converter.body_width = 0  # 禁用自动换行
        converter.single_line_break = True  # 单换行符转换为换行
        converter.ignore_links = False  # 保留链接
        converter.ignore_images = False  # 保留图片
        converter.wrap_list_items = False  # 禁用列表项换行
        converter.ul_item_mark = "-"  # 统一列表符号

        # 转换成md数据
        logger.debug("正在转换HTML为Markdown...")
        markdown_content = converter.handle(html_content)
        logger.debug("Markdown转换完成，长度: %d字符", len(markdown_content))
        # 删除多余空行和空格
        logger.debug("正在清理Markdown格式...")
        markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)
        markdown_content = re.sub(r'[ \t]{2,}', ' ', markdown_content)
        logger.debug("格式清理完成，最终长度: %d字符", len(markdown_content))
        output_path.write_text(markdown_content, encoding="utf-8")
        logger.info("成功保存结果到文件: %s", output_path)

    async def check_login(self, url: str, page):
        """检查登录状态，不执行登录"""
        logger.info("Checking login status at %s", url)
        return await self.unified_login_check(url, page)

    async def perform_login(self, url: str, page, timeout: int = 3):
        """执行登录流程并返回二维码或登录状态
        Args:
            url: 登录页面URL
            page: Playwright page对象
            timeout: 超时时间(秒), 默认60秒
        """
        logger.info("Performing login at %s with timeout %ds", url, timeout)
        try:
            return await self.unified_login_check(url, page)
        finally:
            await self._cleanup()

    @log_and_reraise(logger, "search suggestion reading")
    async def read(self, url: str, keyword: str, page):
        """读取搜索框下拉菜单内容"""
        set_operation_context("search_suggestion_reading")
        logger.debug("开始读取搜索建议，关键词: %s", keyword)
        self.keyword = keyword

        # 初始返回结构
        result = {
            'company': [],
            'page_content': '',
            'status': '',
            'code': 200
        }

        # Navigate to page with error handling
        with error_boundary("page navigation", CrawlerLayerType.CORE):
            logger.debug("导航到目标URL: %s", url)
            try:
                await page.goto(url)
            except Exception as e:
                raise PageLoadException(
                    message="Failed to navigate to target URL",
                    url=url,
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check URL validity and network connectivity"
                )

            logger.debug("等待页面网络空闲状态...")
            try:
                await page.wait_for_load_state('networkidle', timeout=30000)
                logger.debug("页面网络状态已空闲")
            except Exception as e:
                raise TimeoutException(
                    message="Page failed to reach network idle state",
                    operation="page_load_wait",
                    timeout_duration=30.0,
                    details=str(e),
                    original_exception=e
                )

        # Input keyword and get dropdown suggestions
        with error_boundary("keyword input", CrawlerLayerType.CORE):
            logger.debug("正在输入关键词...")
            if not await self.session.safe_fill(page, '#searchKey', self.keyword):
                raise ElementNotFoundException(
                    message="Failed to fill search field",
                    selector="#searchKey",
                    url=url,
                    details="Search field not found or not accessible after retries",
                    suggested_action="Check if page structure has changed"
                )

            logger.debug("关键词输入完成，等待输入完成...")
            await page.wait_for_timeout(1000)  # 确保输入完成

        # Wait for dropdown suggestions with multiple selector attempts
        with error_boundary("dropdown suggestion extraction", CrawlerLayerType.CORE):
            logger.debug("等待下拉建议出现...")
            # 尝试多种选择器变体
            selectors = [
                'div.section-row > div:nth-child(1) > div > a',  # 原始选择器
                'div.search-suggest div.section-row a',         # 更通用的选择器
                'div.search-suggest a.text'                     # 文本链接选择器
            ]

            suggest_items = []
            last_error = None

            for selector in selectors:
                try:
                    logger.debug(f"尝试选择器: {selector}")
                    await page.wait_for_selector(selector, timeout=10000)
                    suggest_items = await page.query_selector_all(selector)
                    if suggest_items:
                        logger.debug(f"使用选择器 {selector} 找到 {len(suggest_items)} 条建议")
                        break
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    last_error = e

            if not suggest_items:
                # Check if login is required
                status = await self.unified_login_check(None, page)
                if not status.get('login', False):
                    raise LoginException(
                        message="User not logged in",
                        login_status="logged_out",
                        details="Login required to access search suggestions",
                        suggested_action="Please log in and try again"
                    )

                # Take debug screenshot and raise appropriate exception
                try:
                    screenshot = await page.screenshot(full_page=True)
                    screenshot_path = f"crawl/{datetime.now().strftime('%Y%m%d_%H%M%S')}_dropdown_error.png"
                    with open(screenshot_path, "wb") as f:
                        f.write(screenshot)
                    logger.info(f"所有选择器尝试失败，截图已保存到: {screenshot_path}")
                except Exception as screenshot_error:
                    logger.warning(f"Failed to take debug screenshot: {screenshot_error}")
                    screenshot_path = None

                raise ElementNotFoundException(
                    message="Unable to locate dropdown suggestion elements",
                    selector=", ".join(selectors),
                    url=url,
                    details=f"All selectors failed. Debug screenshot: {screenshot_path}",
                    context={'attempted_selectors': selectors, 'last_error': str(last_error) if last_error else None},
                    suggested_action="Check if page structure has changed or network issues exist"
                )
            
        # Extract company names from suggestions
        with error_boundary("company name extraction", CrawlerLayerType.CORE):
            logger.debug("获取到%d条建议项", len(suggest_items))
            company_names = []
            logger.debug("开始提取公司名称...")

            matched = False
            for item in suggest_items:
                try:
                    text_element = await item.query_selector('.text')
                    if text_element:
                        content = await text_element.text_content()
                        content = content.strip()
                        company_names.append(content)

                        # 精确匹配关键词
                        if self.keyword and content == self.keyword.strip():
                            logger.info("找到完全匹配项: %s", content)
                            logger.debug("正在点击匹配项...")

                            with error_boundary("suggestion item click", CrawlerLayerType.CORE):
                                await item.click(delay=50, force=True)
                                logger.info("成功跳转到详情页: %s", page.url)
                                logger.debug("等待5秒让页面加载...")
                                await page.wait_for_timeout(5000)
                                logger.debug("页面加载等待完成")

                                # 处理目标页面结果
                                await self._process_target_results(page, content)
                                matched = True
                                break
                except Exception as e:
                    logger.warning(f"Failed to extract content from suggestion item: {e}")
                    continue
            
        # Handle fallback selection if no exact match
        with error_boundary("fallback suggestion selection", CrawlerLayerType.CORE):
            if not matched:
                if len(suggest_items) == 1:
                    item = suggest_items[0]
                    try:
                        text_element = await item.query_selector('.text')
                        if text_element:
                            content = await text_element.text_content()
                            content = content.strip()
                            if '公司' in content:
                                # 唯一匹配关键词
                                logger.info("找到唯一匹配项: %s", content)
                                logger.debug("正在点击匹配项...")
                                await item.click(delay=50, force=True)
                                logger.info("成功跳转到详情页: %s", page.url)
                                logger.debug("等待5秒让页面加载...")
                                await page.wait_for_timeout(5000)
                                logger.debug("页面加载等待完成")

                                # 处理目标页面结果
                                await self._process_target_results(page, content)
                    except Exception as e:
                        raise SearchException(
                            message="Failed to process single suggestion item",
                            keyword=self.keyword,
                            search_type="single_item",
                            details=str(e),
                            original_exception=e
                        )
                else:
                    logger.info("没有找到完全匹配项，随机选择一个公司点击")
                    try:
                        random_item = random.choice(suggest_items)
                        await page.wait_for_timeout(100)
                        await random_item.click(delay=50, force=True)
                        await page.wait_for_timeout(1000)

                        result.update({
                            'company': company_names,
                            'status': 'success',
                            'code': 201
                        })
                        return result
                    except Exception as e:
                        raise SearchException(
                            message="Failed to click random suggestion item",
                            keyword=self.keyword,
                            search_type="random_selection",
                            details=str(e),
                            original_exception=e
                        )

        # Process results and read output file
        with error_boundary("result file processing", CrawlerLayerType.CORE):
            result.update({
                'company': company_names,
                'status': 'success',
                'code': 200
            })

            # 读取处理后的结果文件
            if 'content' in locals():
                filename = sanitize_filename(content) + ".md"
                output_path = Path(self.output_dir) / filename

                try:
                    if output_path.exists():
                        markdown_content = output_path.read_text(encoding="utf-8")
                        result.update({
                            'page_content': markdown_content,
                            'code': 202
                        })
                    else:
                        result.update({
                            'page_content': '',
                            'code': 203,
                            'status': f'找不到目标文件：{output_path}'
                        })
                except Exception as e:
                    logger.warning(f"Failed to read output file {output_path}: {e}")
                    result.update({
                        'page_content': '',
                        'code': 204,
                        'status': f'读取文件失败：{str(e)}'
                    })

            return result

    @log_and_reraise(logger, "unified login check")
    async def unified_login_check(self, url: str, page):
        """统一登录状态检查方法
        Args:
            url: 目标URL
            page: Playwright page对象
        Returns:
            {
                'login': bool,  # 登录状态
                'status': str,  # 状态描述
                'screen': str   # base64编码的截图(仅当login=False)
            }
        """
        set_operation_context("unified_login_check")
        logger.info(f"执行统一登录检查，URL: {url}")
        result = {'login': False, 'status': 'initial', 'screen': None}

        # Navigate to target URL if provided
        if url:
            with error_boundary("login page navigation", CrawlerLayerType.CORE):
                logger.debug(f"going to {url}")
                try:
                    await page.goto(url)
                    await page.wait_for_timeout(5000)
                    logger.debug(f"访问URL：{url}")
                except Exception as e:
                    raise PageLoadException(
                        message="Failed to navigate to login page",
                        url=url,
                        details=str(e),
                        original_exception=e
                    )

        # Take screenshot and prepare page
        with error_boundary("page screenshot and preparation", CrawlerLayerType.CORE):
            try:
                screenshot = await page.screenshot(full_page=True)
                # 刷新页面内容确保最新状态
                await page.evaluate("() => window.scrollTo(0, 0)")
                await page.wait_for_timeout(1000)  # 等待页面更新
            except Exception as e:
                raise BrowserException(
                    message="Failed to take screenshot or prepare page",
                    browser_action="screenshot_and_scroll",
                    details=str(e),
                    original_exception=e
                )

        # Check account restriction status
        with error_boundary("account restriction check", CrawlerLayerType.CORE):
            limit_status = await self._check_account_limits(page, screenshot)
            if limit_status:
                return limit_status
            
        # Check and handle login button
        with error_boundary("login button check", CrawlerLayerType.CORE):
            login_button_selector = 'button.qccd-btn.qccd-btn-primary.qcc-header-login-btn'
            if await page.is_visible(login_button_selector):
                try:
                    logger.info("检测到登录按钮，尝试点击")
                    await page.click(login_button_selector, timeout=1000)
                    await page.wait_for_timeout(1000)  # 等待页面响应
                    screenshot = await page.screenshot(full_page=True)
                    result.update({
                        'status': 'qrcode_detected',
                        'screen': base64.b64encode(screenshot).decode('utf-8')
                    })
                    return result
                except Exception as e:
                    logger.warning(f"登录按钮点击失败: {str(e)}")
                    # Continue with other checks instead of failing

        # Check for QR code login interface
        with error_boundary("qrcode interface check", CrawlerLayerType.CORE):
            qrcode_selectors = [
                'div.qcc-login-qrcode-area',
                'div.login-qrcode',
                'div.qrcode-container',
                'div.qrcode-wrapper',
                'div.qrcode',
                'div.qrcoderefresh'
            ]
            for selector in qrcode_selectors:
                if await page.is_visible(selector):
                    logger.debug("检测到二维码登录界面")
                    result.update({
                        'status': 'qrcode_detected',
                        'screen': base64.b64encode(screenshot).decode('utf-8')
                    })
                    return result

        # Check input field status and perform login test
        with error_boundary("input field login test", CrawlerLayerType.CORE):
            input_selectors = ['#searchKey', 'input[type="text"]', 'input[type="search"]']
            for selector in input_selectors:
                # Check and close modal if present
                modal_close_selector = 'i.modal-close.campaign.aicon.qccdicon'
                if await page.is_visible(modal_close_selector):
                    logger.info("检测到弹窗，尝试关闭")
                    try:
                        await page.click(modal_close_selector, timeout=1000)
                        await page.wait_for_timeout(1000)
                    except Exception as e:
                        logger.warning(f"弹窗关闭失败: {e}")
                        # Continue with login check

                vis = await page.is_visible(selector)
                ena = await page.is_enabled(selector)
                logger.info(f"检测到输入框: {selector}, 可见: {vis}, 可用: {ena}")

                if all([vis, ena]):
                    # Try clicking "查一下" button to test login status
                    try:
                        await page.click('text=查一下', timeout=1000)
                        await page.wait_for_timeout(1000)
                        logger.debug(f"点击 查一下")

                        # Check for QR code after click
                        qrcode_selectors = [
                            'div.qcc-login-qrcode-area',
                            'div.login-qrcode',
                            'div.qrcode-container',
                            'div.qrcode-wrapper',
                            'div.qrcode',
                            'div.qrcoderefresh'
                        ]

                        for qr_selector in qrcode_selectors:
                            logger.debug(f"检测selector： {qr_selector}")
                            if await page.is_visible(qr_selector) and not await page.is_visible("text=查一下"):
                                logger.info("点击后出现二维码登录界面")
                                screenshot = await page.screenshot(full_page=True)
                                result.update({
                                    'status': 'qrcode_after_click',
                                    'screen': base64.b64encode(screenshot).decode('utf-8')
                                })
                                return result

                        # Check account limits after click
                        limit_status = await self._check_account_limits(page, screenshot)
                        if limit_status:
                            return limit_status

                    except Exception as e:
                        logger.debug(f"查一下失败: {str(e)}")
                        screenshot = await page.screenshot(full_page=True)
                        result.update({
                            'status': 'click_failed',
                            'screen': base64.b64encode(screenshot).decode('utf-8')
                        })
                        return result

                    # All checks passed - user is logged in
                    logger.info("用户已登录")
                    return {'login': True, 'status': 'logged_in'}

        # Default failure case
        with error_boundary("default failure handling", CrawlerLayerType.CORE):
            screenshot = await page.screenshot(full_page=True)
            result.update({
                'status': 'default_failed',
                'screen': base64.b64encode(screenshot).decode('utf-8')
            })
            return result

    # 统一检测方法
    async def detect_message(self, page, text, element_class=None):
        detected = False
        selectors = [
            f"text='{text}'",  # 精确匹配
            f":has-text('{text}')",  # 部分匹配
        ]
        
        if element_class:
            selectors.append(f"{element_class}")  # class匹配
        
        # 检测主文档、iframe和shadow DOM
        contexts = [
            lambda s: page.locator(s),
            lambda s: page.frame_locator("iframe").locator(s),
            lambda s: page.locator(f"*::shadow=text='{text}'"),
        ]
        
        for selector in selectors:
            for context in contexts:
                try:
                    locator = context(selector)
                    await locator.wait_for(timeout=100)
                    if await locator.count() > 0:
                        detected = True
                        logger.debug(f"检测到限制提示 - 文本: {text}, 选择器: {selector}")
                        return True
                except Exception as e:
                    logger.debug(f"检测失败 - 文本: {text}, 选择器: {selector}")
        
        # 如果未检测到但页面有相关文本
        page_text = await page.content()
        if text in page_text:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            debug_path = f"crawl/limit_debug_{timestamp}"
            await page.screenshot(path=f"{debug_path}.png", full_page=True)
            await page.locator("body").screenshot(path=f"{debug_path}_body.png")
            with open(f"{debug_path}.html", "w", encoding="utf-8") as f:
                f.write(page_text)
            logger.error(f"检测失败但文本存在 - {text}, 调试文件已保存到: {debug_path}.*")
        
        return detected
    async def _check_account_limits(self, page, screenshot):
        """统一账号限制检测方法"""
        # 检测两种限制提示
        risk_control = await self.detect_message(page, "账号使用异常，已限制继续访问")
        rate_limit = await self.detect_message(page, "您的操作过于频繁，验证后再操作")
        
        # 综合判断
        if any([risk_control, rate_limit]):
            status = 'permanent_failed' if risk_control else 'rate_limited'
            logger.warning(f"检测到账号限制: {status}")
            return {
                'login': False,
                'status': status,
                'screen': base64.b64encode(screenshot).decode('utf-8')
            }
        return None

    async def _cleanup(self):
        """Clean up any resources managed by this instance"""
        logger.debug("Cleaning up resources...")
        try:
            if hasattr(self, 'context') and self.context:
                logger.debug("Closing context...")
                await self.context.close()
                self.context = None
                logger.debug("Context closed")
        except Exception as e:
            logger.error("Error closing context", exc_info=True)

    async def reset_browser(self):
        """Force reset browser instance"""
        logger.info("Resetting browser instance")
        try:
            if hasattr(self, 'context') and self.context:
                await self._cleanup()
                logger.info("Browser instance reset completed")
                return True
            return False
        except Exception as e:
            logger.error("Error resetting browser", exc_info=True)
            raise
