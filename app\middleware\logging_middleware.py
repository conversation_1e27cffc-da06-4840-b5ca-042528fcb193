import logging
from fastapi import Request, Response
from app.logging_config import Sensitive<PERSON><PERSON><PERSON><PERSON><PERSON>, setup_logging, set_layer_context, set_operation_context

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, PresentationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for middleware operations
set_layer_context("presentation")

sensitive_filter = SensitiveDataFilter()

@log_and_reraise(logger, "request logging middleware")
async def log_requests(request: Request, call_next):
    """Request logging middleware with unified error handling"""
    set_operation_context("request_logging_middleware")

    # Log incoming request
    with error_boundary("request logging", LayerType.PRESENTATION):
        try:
            client = f"{request.client.host}:{request.client.port}" if request.client else "unknown"
            log_msg = f"Request: {request.method} {request.url.path} from {client}"
            logger.debug(sensitive_filter.mask(log_msg))
        except Exception as e:
            # Don't fail the request if logging fails
            logger.warning(f"Failed to log incoming request: {str(e)}")

    # Process request
    with error_boundary("request processing", LayerType.PRESENTATION):
        try:
            response = await call_next(request)
        except Exception as e:
            # Log the error but let it propagate
            logger.error(f"Request processing failed: {str(e)}")
            raise

    # Log outgoing response
    with error_boundary("response logging", LayerType.PRESENTATION):
        try:
            logger.debug(sensitive_filter.mask(f"Response: {response.status_code} for {request.method} {request.url.path}"))
        except Exception as e:
            # Don't fail the request if logging fails
            logger.warning(f"Failed to log outgoing response: {str(e)}")

    return response
